#!/usr/bin/env python3
"""
Data analysis script for extracted phone information
"""

import json
import csv
import re
from collections import Counter, defaultdict
from typing import Dict, List, Any

class PhoneDataAnalyzer:
    def __init__(self, csv_file: str = "phone_data.csv", json_file: str = "phone_data.json"):
        self.csv_file = csv_file
        self.json_file = json_file
        self.data = []
        
    def load_data(self) -> bool:
        """Load data from JSON file"""
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ Loaded {len(self.data)} records from {self.json_file}")
            return True
        except FileNotFoundError:
            print(f"❌ File {self.json_file} not found")
            return False
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return False
    
    def analyze_phone_patterns(self) -> Dict[str, Any]:
        """Analyze phone number patterns"""
        if not self.data:
            return {}
        
        phone_lengths = Counter()
        phone_prefixes = Counter()
        
        for record in self.data:
            phone = record.get('phone', '')
            if phone:
                phone_lengths[len(phone)] += 1
                
                # Analyze prefixes for different lengths
                if len(phone) == 7:
                    phone_prefixes[f"7-digit-{phone[:3]}"] += 1
                elif len(phone) == 11:
                    phone_prefixes[f"11-digit-{phone[:3]}"] += 1
        
        return {
            'total_phones': len(self.data),
            'length_distribution': dict(phone_lengths),
            'prefix_distribution': dict(phone_prefixes.most_common(10))
        }
    
    def analyze_organizations(self) -> Dict[str, Any]:
        """Analyze organizational structure"""
        if not self.data:
            return {}
        
        org_levels = defaultdict(int)
        org_hierarchy = defaultdict(list)
        top_level_orgs = Counter()
        
        for record in self.data:
            org_unit = record.get('org_unit', '')
            if org_unit:
                levels = org_unit.split('-')
                org_levels[len(levels)] += 1
                
                # Track top-level organizations
                if levels:
                    top_level_orgs[levels[0]] += 1
                
                # Track full hierarchy
                org_hierarchy[levels[0]].append(org_unit)
        
        return {
            'total_organizations': len(set(record.get('org_unit', '') for record in self.data)),
            'hierarchy_levels': dict(org_levels),
            'top_level_distribution': dict(top_level_orgs.most_common()),
            'hierarchy_sample': {k: list(set(v))[:5] for k, v in list(org_hierarchy.items())[:5]}
        }
    
    def analyze_contacts(self) -> Dict[str, Any]:
        """Analyze contact person information"""
        if not self.data:
            return {}
        
        contact_names = []
        name_lengths = Counter()
        common_titles = Counter()
        
        title_patterns = [
            r'(厂长|部长|科长|主任|经理|主管|副厂长|副部长|副科长|副主任|副经理)',
            r'(书记|副书记|主席|副主席)',
            r'(工程师|技术员|操作员|维修工|司机)'
        ]
        
        for record in self.data:
            contact = record.get('contact_name', '')
            if contact:
                contact_names.append(contact)
                name_lengths[len(contact)] += 1
                
                # Extract titles
                for pattern in title_patterns:
                    matches = re.findall(pattern, contact)
                    for match in matches:
                        common_titles[match] += 1
        
        return {
            'total_contacts': len([c for c in contact_names if c]),
            'empty_contacts': len([c for c in contact_names if not c]),
            'name_length_distribution': dict(name_lengths),
            'common_titles': dict(common_titles.most_common(10)),
            'sample_names': contact_names[:10]
        }
    
    def find_duplicates(self) -> Dict[str, Any]:
        """Find duplicate phone numbers and contacts"""
        if not self.data:
            return {}
        
        phone_counts = Counter()
        contact_counts = Counter()
        
        for record in self.data:
            phone = record.get('phone', '')
            contact = record.get('contact_name', '')
            
            if phone:
                phone_counts[phone] += 1
            if contact:
                contact_counts[contact] += 1
        
        duplicate_phones = {k: v for k, v in phone_counts.items() if v > 1}
        duplicate_contacts = {k: v for k, v in contact_counts.items() if v > 1}
        
        return {
            'duplicate_phones': duplicate_phones,
            'duplicate_contacts': duplicate_contacts,
            'total_duplicates': len(duplicate_phones) + len(duplicate_contacts)
        }
    
    def generate_report(self) -> str:
        """Generate comprehensive analysis report"""
        if not self.load_data():
            return "❌ Failed to load data for analysis"
        
        phone_analysis = self.analyze_phone_patterns()
        org_analysis = self.analyze_organizations()
        contact_analysis = self.analyze_contacts()
        duplicate_analysis = self.find_duplicates()
        
        report = []
        report.append("📊 Phone Data Analysis Report")
        report.append("=" * 50)
        
        # Phone Analysis
        report.append("\n📞 Phone Number Analysis:")
        report.append(f"  Total phone numbers: {phone_analysis.get('total_phones', 0)}")
        report.append("  Length distribution:")
        for length, count in phone_analysis.get('length_distribution', {}).items():
            report.append(f"    {length} digits: {count} numbers")
        
        report.append("  Common prefixes:")
        for prefix, count in list(phone_analysis.get('prefix_distribution', {}).items())[:5]:
            report.append(f"    {prefix}: {count} numbers")
        
        # Organization Analysis
        report.append("\n🏢 Organizational Analysis:")
        report.append(f"  Total organizations: {org_analysis.get('total_organizations', 0)}")
        report.append("  Hierarchy levels:")
        for level, count in org_analysis.get('hierarchy_levels', {}).items():
            report.append(f"    {level} levels: {count} units")
        
        report.append("  Top-level organizations:")
        for org, count in list(org_analysis.get('top_level_distribution', {}).items())[:10]:
            report.append(f"    {org}: {count} entries")
        
        # Contact Analysis
        report.append("\n👤 Contact Person Analysis:")
        report.append(f"  Total contacts: {contact_analysis.get('total_contacts', 0)}")
        report.append(f"  Empty contacts: {contact_analysis.get('empty_contacts', 0)}")
        
        report.append("  Common titles:")
        for title, count in list(contact_analysis.get('common_titles', {}).items())[:5]:
            report.append(f"    {title}: {count} occurrences")
        
        # Duplicate Analysis
        report.append("\n🔍 Duplicate Analysis:")
        report.append(f"  Duplicate phone numbers: {len(duplicate_analysis.get('duplicate_phones', {}))}")
        report.append(f"  Duplicate contact names: {len(duplicate_analysis.get('duplicate_contacts', {}))}")
        
        if duplicate_analysis.get('duplicate_phones'):
            report.append("  Duplicate phones:")
            for phone, count in list(duplicate_analysis['duplicate_phones'].items())[:5]:
                report.append(f"    {phone}: {count} times")
        
        # Data Quality
        report.append("\n✅ Data Quality Summary:")
        total_records = len(self.data)
        complete_records = len([r for r in self.data if r.get('phone') and r.get('org_unit') and r.get('contact_name')])
        completeness = (complete_records / total_records * 100) if total_records > 0 else 0
        
        report.append(f"  Total records: {total_records}")
        report.append(f"  Complete records: {complete_records}")
        report.append(f"  Data completeness: {completeness:.1f}%")
        
        return "\n".join(report)
    
    def save_report(self, filename: str = "analysis_report.txt"):
        """Save analysis report to file"""
        report = self.generate_report()
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📄 Analysis report saved to {filename}")
        return report

def main():
    """Main analysis function"""
    analyzer = PhoneDataAnalyzer()
    
    # Generate and display report
    report = analyzer.generate_report()
    print(report)
    
    # Save report to file
    analyzer.save_report()

if __name__ == "__main__":
    main()
