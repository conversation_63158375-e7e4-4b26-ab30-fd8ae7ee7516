#!/usr/bin/env python3
"""
Simple runner script for the phone scraper (Simple mode - no external dependencies)
"""

import asyncio
import sys
from phone_scraper_simple import PhoneScraperSimple

async def test_target_url():
    """Simple test for target URL accessibility"""
    import aiohttp
    
    base_url = "http://***********/phone/20201227.html"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(base_url, timeout=10) as response:
                if response.status == 200:
                    # Try different encodings for Chinese websites
                    try:
                        content = await response.text(encoding='utf-8')
                    except UnicodeDecodeError:
                        try:
                            content = await response.text(encoding='gbk')
                        except UnicodeDecodeError:
                            content = await response.text(encoding='gb2312')
                    
                    # Check for phone-related content
                    if "电话" in content or "phone" in content.lower() or "联系" in content:
                        print("SUCCESS: Target URL is accessible and contains phone data")
                        return True
                    else:
                        print("WARNING: Target URL accessible but no phone data found")
                        return False
                else:
                    print(f"ERROR: Target URL returned status: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"ERROR: Failed to access target URL: {e}")
        return False

async def main():
    print("Phone Information Scraper (Simple Mode)")
    print("=" * 50)
    print("This version uses direct HTTP requests and doesn't require any external services")
    
    # Check if user wants to run tests first
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        print("Running simple connectivity test...")
        success = await test_target_url()
        if not success:
            print("ERROR: Target URL test failed. Please check network connectivity.")
            return
        print("\n" + "=" * 50)
    
    # Run the scraper
    print("Starting phone information extraction...")
    print("All logs will be written to phone_scraper_simple.log")
    scraper = PhoneScraperSimple()
    
    try:
        data = await scraper.scrape_all_data()
        
        if data:
            print(f"\nSUCCESS: Successfully extracted {len(data)} phone records!")
            
            # Save results
            scraper.save_to_csv()
            scraper.save_to_json()
            scraper.print_summary()
            
            print(f"\nResults saved to:")
            print(f"  - phone_data_simple.csv")
            print(f"  - phone_data_simple.json")
            print(f"  - phone_scraper_simple.log")
            
        else:
            print("ERROR: No data was extracted. Check phone_scraper_simple.log for details.")
            
    except KeyboardInterrupt:
        print("\nINTERRUPTED: Scraping interrupted by user")
    except Exception as e:
        print(f"ERROR: Scraping failed: {e}")
        print("Check phone_scraper_simple.log for detailed error information.")
        raise

if __name__ == "__main__":
    # Usage instructions
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help"]:
        print("Usage:")
        print("  python run_scraper_simple.py           # Run scraper directly")
        print("  python run_scraper_simple.py --test    # Run test first, then scraper")
        print("  python run_scraper_simple.py --help    # Show this help")
        sys.exit(0)
    
    asyncio.run(main())
