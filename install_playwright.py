#!/usr/bin/env python3
"""
Install Playwright browsers for crawl4ai
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"Running: {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"SUCCESS: {description} completed")
        if result.stdout:
            print(f"Output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {description} failed")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Install Playwright browsers"""
    print("Playwright Browser Installation")
    print("=" * 40)
    
    commands = [
        ("pip install playwright", "Installing Playwright"),
        ("playwright install", "Installing Playwright browsers"),
        ("playwright install-deps", "Installing system dependencies")
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            print(f"Failed to execute: {command}")
            return False
    
    print("\nSUCCESS: Playwright installation completed!")
    print("You can now run the scraper with: python run_scraper.py --test")
    return True

if __name__ == "__main__":
    main()
