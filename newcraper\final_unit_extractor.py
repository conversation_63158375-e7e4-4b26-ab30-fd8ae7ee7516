#!/usr/bin/env python3
"""
最终版本：精确提取6个区域的单位名称和子链接
基于HTML结构分析的结果，准确提取表格11-15和项目经理部
"""

import asyncio
import csv
import json
from crawl4ai import AsyncWebCrawler
from bs4 import BeautifulSoup
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('final_unit_extractor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FinalUnitExtractor:
    def __init__(self):
        self.url = "http://172.18.1.16/phone/20201227.html"
        self.base_url = "http://172.18.1.16"
        self.units_data = []
        
        # 根据HTML分析结果，定义表格索引映射
        self.table_mapping = {
            "机关部室办公电话": 11,      # 表格11 (21个链接)
            "主要生产单位办公电话": 12,   # 表格12 (8个链接)
            "主要经营企业办公电话": 13,   # 表格13 (3个链接)
            "辅助生产企业办公电话": 14,   # 表格14 (6个链接)
            "主要服务单位办公电话": 15    # 表格15 (2个链接)
        }

    async def extract_units(self):
        """提取单位信息"""
        logger.info(f"开始提取单位信息: {self.url}")
        
        async with AsyncWebCrawler(verbose=True) as crawler:
            try:
                # 爬取网页
                result = await crawler.arun(url=self.url)
                
                if not result.success:
                    logger.error(f"爬取失败: {result.error_message}")
                    return []
                
                logger.info("网页爬取成功，开始解析HTML")
                
                # 解析HTML
                soup = BeautifulSoup(result.html, 'html.parser')
                
                # 提取各个区域的单位
                self._extract_table_units(soup)
                self._extract_project_manager_unit(soup)
                
                logger.info(f"提取完成，共获得 {len(self.units_data)} 个单位")
                return self.units_data
                
            except Exception as e:
                logger.error(f"提取过程出错: {str(e)}")
                return []

    def _extract_table_units(self, soup):
        """提取表格中的单位信息"""
        
        # 获取所有表格
        tables = soup.find_all('table')
        logger.info(f"找到 {len(tables)} 个表格")
        
        # 提取各个区域的表格
        for region_name, table_index in self.table_mapping.items():
            logger.info(f"提取 {region_name} (表格{table_index})")
            
            if table_index <= len(tables):
                table = tables[table_index - 1]  # 表格索引从0开始
                
                # 查找表格中的所有链接
                links = table.find_all('a', href=True)
                logger.info(f"  表格{table_index} 找到 {len(links)} 个链接")
                
                for link in links:
                    unit_name = link.get_text(strip=True)
                    unit_href = link.get('href')
                    
                    if unit_name and unit_href:
                        # 构建完整URL
                        if unit_href.startswith('/'):
                            full_url = self.base_url + unit_href
                        elif unit_href.startswith('http'):
                            full_url = unit_href
                        else:
                            full_url = f"{self.base_url}/phone/{unit_href}"
                        
                        unit_data = {
                            '单位名称': unit_name,
                            '子链接': full_url
                        }
                        
                        # 避免重复
                        if unit_data not in self.units_data:
                            self.units_data.append(unit_data)
                            logger.info(f"    ✓ {unit_name} -> {full_url}")
                
                logger.info(f"  {region_name} 提取完成，共 {len([u for u in self.units_data if region_name in u.get('区域', '')])} 个单位")
            else:
                logger.warning(f"未找到表格{table_index}")

    def _extract_project_manager_unit(self, soup):
        """提取项目经理部单位信息"""
        logger.info("提取项目经理部办公电话")
        
        # 查找包含"项目经理部"的链接
        project_links = soup.find_all('a', href=True)
        
        for link in project_links:
            link_text = link.get_text(strip=True)
            if '项目经理部' in link_text:
                unit_href = link.get('href')
                
                # 构建完整URL
                if unit_href.startswith('/'):
                    full_url = self.base_url + unit_href
                elif unit_href.startswith('http'):
                    full_url = unit_href
                else:
                    full_url = f"{self.base_url}/phone/{unit_href}"
                
                unit_data = {
                    '单位名称': link_text,
                    '子链接': full_url
                }
                
                # 避免重复
                if unit_data not in self.units_data:
                    self.units_data.append(unit_data)
                    logger.info(f"    ✓ {link_text} -> {full_url}")
                break

    def save_data(self):
        """保存数据"""
        if not self.units_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存CSV文件 - 只保留单位名称和子链接两列
        csv_file = 'newcraper/final_units_data.csv'
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['单位名称', '子链接']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            # 只写入需要的两列
            for unit in self.units_data:
                writer.writerow({
                    '单位名称': unit['单位名称'],
                    '子链接': unit['子链接']
                })
        
        # 保存JSON文件
        json_file = 'newcraper/final_units_data.json'
        clean_data = []
        for unit in self.units_data:
            clean_data.append({
                '单位名称': unit['单位名称'],
                '子链接': unit['子链接']
            })
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(clean_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据已保存到:")
        logger.info(f"  - {csv_file}")
        logger.info(f"  - {json_file}")
        
        # 生成统计报告
        self._generate_report()

    def _generate_report(self):
        """生成统计报告"""
        logger.info("\n" + "="*80)
        logger.info("📊 最终单位提取报告")
        logger.info("="*80)
        
        total_units = len(self.units_data)
        
        logger.info(f"\n📈 总体统计:")
        logger.info(f"   提取单位总数: {total_units}")
        logger.info(f"   数据格式: 只包含'单位名称'和'子链接'两列")
        logger.info(f"   数据质量: 无重复，无冗余列")
        
        # 按链接路径分类统计
        path_stats = {}
        for unit in self.units_data:
            link = unit['子链接']
            if '机关部室' in link:
                category = "机关部室办公电话"
            elif '主要生产单位' in link:
                category = "主要生产单位办公电话"
            elif '主要经营企业' in link:
                category = "主要经营企业办公电话"
            elif '辅助生产企业' in link:
                category = "辅助生产企业办公电话"
            elif '主要服务单位' in link:
                category = "主要服务单位办公电话"
            elif 'xmjlb' in link:
                category = "项目经理部办公电话"
            else:
                category = "其他"
            
            if category not in path_stats:
                path_stats[category] = []
            path_stats[category].append(unit['单位名称'])
        
        logger.info(f"\n📋 分类统计:")
        for category, units in path_stats.items():
            logger.info(f"   ✅ {category}: {len(units)} 个单位")
            logger.info(f"      单位: {', '.join(units[:3])}")
            if len(units) > 3:
                logger.info(f"      ... 还有 {len(units)-3} 个单位")
        
        # 验证预期数量
        expected_counts = {
            "机关部室办公电话": 21,
            "主要生产单位办公电话": 8,
            "主要经营企业办公电话": 3,
            "辅助生产企业办公电话": 6,
            "主要服务单位办公电话": 2,
            "项目经理部办公电话": 1
        }
        
        logger.info(f"\n🎯 数量验证:")
        total_expected = sum(expected_counts.values())
        for category, expected in expected_counts.items():
            actual = len(path_stats.get(category, []))
            status = "✅" if actual == expected else "⚠️"
            logger.info(f"   {status} {category}: {actual}/{expected}")
        
        logger.info(f"\n📊 总计: {total_units}/{total_expected} = {(total_units/total_expected)*100:.1f}%")

async def main():
    """主函数"""
    extractor = FinalUnitExtractor()
    
    # 提取单位信息
    units_data = await extractor.extract_units()
    
    if units_data:
        # 保存数据
        extractor.save_data()
        logger.info("🎉 最终单位提取任务完成！")
        logger.info("💡 生成的数据只包含'单位名称'和'子链接'两列，符合您的要求")
    else:
        logger.error("❌ 提取失败，没有获得任何数据")

if __name__ == "__main__":
    asyncio.run(main())
