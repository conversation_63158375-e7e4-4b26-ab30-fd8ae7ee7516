#!/usr/bin/env python3
"""
Setup script for Phone Information Scraper
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible. Need Python 3.8+")
        return False

def install_dependencies():
    """Install required Python packages"""
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python dependencies"
    )

def check_docker():
    """Check if Docker is available"""
    return run_command("docker --version", "Checking Docker installation")

def setup_crawl4ai():
    """Setup Crawl4AI Docker container"""
    print("🐳 Setting up Crawl4AI Docker container...")
    
    # Pull the image
    if not run_command(
        "docker pull unclecode/crawl4ai:all-amd64",
        "Pulling Crawl4AI Docker image"
    ):
        return False
    
    # Check if container is already running
    result = subprocess.run(
        "docker ps --filter 'name=crawl4ai' --format '{{.Names}}'",
        shell=True, capture_output=True, text=True
    )
    
    if "crawl4ai" in result.stdout:
        print("✅ Crawl4AI container is already running")
        return True
    
    # Start the container
    return run_command(
        "docker run -d --name crawl4ai -e CRAWL4AI_API_TOKEN=123456 -p 11235:11235 unclecode/crawl4ai:all-amd64",
        "Starting Crawl4AI container"
    )

def test_setup():
    """Test the complete setup"""
    print("🧪 Testing setup...")
    return run_command(
        f"{sys.executable} test_connection.py",
        "Running connection tests"
    )

def main():
    """Main setup function"""
    print("📞 Phone Information Scraper Setup")
    print("=" * 50)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing dependencies", install_dependencies),
        ("Checking Docker", check_docker),
        ("Setting up Crawl4AI", setup_crawl4ai),
        ("Testing setup", test_setup)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        if not step_func():
            failed_steps.append(step_name)
            print(f"❌ {step_name} failed")
        else:
            print(f"✅ {step_name} completed")
    
    print("\n" + "=" * 50)
    
    if failed_steps:
        print("❌ Setup completed with errors:")
        for step in failed_steps:
            print(f"  - {step}")
        print("\nPlease fix the issues above before running the scraper.")
        return False
    else:
        print("🎉 Setup completed successfully!")
        print("\nYou can now run the scraper with:")
        print("  python run_scraper.py --test")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
