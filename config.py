"""
Configuration file for Phone Scraper
"""

# Target URLs
BASE_URL = "http://172.18.1.16/phone/20201227.html"
CRAWL4AI_URL = "http://172.18.151.239:11235"
API_TOKEN = "123456"

# Scraping settings
REQUEST_DELAY = 1  # seconds between requests
TIMEOUT = 30  # seconds
MAX_RETRIES = 3

# Phone number patterns
PHONE_PATTERNS = [
    r'\b\d{7}\b',      # 7-digit internal numbers
    r'\b\d{11}\b',     # 11-digit numbers
    r'\b1[3-9]\d{9}\b' # Mobile numbers starting with 1
]

# Exclusion keywords
EXCLUSION_KEYWORDS = [
    "4G专网手机号码", 
    "第二人民医院急救电话", 
    "矿消防火警电话",
    "办公室电话", 
    "传真", 
    "邮编",
    "邮箱",
    "email"
]

# Organizational unit keywords for link filtering
ORG_KEYWORDS = [
    "厂", "部", "室", "科", "中心", "公司", "场", 
    "矿", "处", "站", "队", "组", "班", "所"
]

# Output settings
OUTPUT_CSV = "phone_data.csv"
OUTPUT_JSON = "phone_data.json"
LOG_FILE = "phone_scraper.log"

# Headers for CSV output
CSV_HEADERS = ['phone_number', 'organizational_unit', 'contact_person', 'source_url']
