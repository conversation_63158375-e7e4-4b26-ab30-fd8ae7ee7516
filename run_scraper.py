#!/usr/bin/env python3
"""
Simple runner script for the phone scraper
"""

import asyncio
import sys
from phone_scraper import PhoneScraper
from test_simple import run_simple_tests

async def main():
    print("Phone Information Scraper")
    print("=" * 50)

    # Check if user wants to run tests first
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        print("Running connection tests...")
        success = await run_simple_tests()
        if not success:
            print("ERROR: Tests failed. Please fix configuration before running scraper.")
            print("Check simple_test.log for details.")
            return
        print("\n" + "=" * 50)

    # Run the scraper
    print("Starting phone information extraction...")
    print("All logs will be written to phone_scraper.log")
    scraper = PhoneScraper()

    try:
        data = await scraper.scrape_all_data()

        if data:
            print(f"\nSUCCESS: Successfully extracted {len(data)} phone records!")

            # Save results
            scraper.save_to_csv()
            scraper.save_to_json()
            scraper.print_summary()

            print(f"\nResults saved to:")
            print(f"  - phone_data.csv")
            print(f"  - phone_data.json")
            print(f"  - phone_scraper.log")

        else:
            print("ERROR: No data was extracted. Check phone_scraper.log for details.")

    except KeyboardInterrupt:
        print("\nINTERRUPTED: Scraping interrupted by user")
    except Exception as e:
        print(f"ERROR: Scraping failed: {e}")
        print("Check phone_scraper.log for detailed error information.")
        raise

if __name__ == "__main__":
    # Usage instructions
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help"]:
        print("Usage:")
        print("  python run_scraper.py           # Run scraper directly")
        print("  python run_scraper.py --test    # Run tests first, then scraper")
        print("  python run_scraper.py --help    # Show this help")
        sys.exit(0)

    asyncio.run(main())
