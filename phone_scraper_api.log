2025-05-25 11:11:25,355 - INFO - RUNNING: Crawl4AI Health test...
2025-05-25 11:11:25,355 - INFO - Testing health endpoint: http://172.18.151.239:11235/health
2025-05-25 11:11:25,360 - INFO - SUCCESS: Crawl4AI service is healthy
2025-05-25 11:11:25,361 - INFO - PASS: Crawl4AI Health test passed
2025-05-25 11:11:25,361 - INFO - RUNNING: Crawl4AI API test...
2025-05-25 11:11:25,361 - INFO - Testing API endpoint: http://172.18.151.239:11235/crawl
2025-05-25 11:11:25,366 - INFO - SUCCESS: Crawl4AI API is working
2025-05-25 11:11:25,366 - INFO - PASS: Crawl4AI API test passed
2025-05-25 11:11:25,366 - INFO - RUNNING: Target URL test...
2025-05-25 11:11:25,367 - INFO - Testing target URL accessibility: http://172.18.1.16/phone/20201227.html
2025-05-25 11:11:25,380 - INFO - SUCCESS: Target URL is accessible
2025-05-25 11:11:25,380 - INFO - SUCCESS: Found phone-related content
2025-05-25 11:11:25,381 - INFO - PASS: Target URL test passed
2025-05-25 11:11:25,381 - INFO - SUMMARY: Test Results Summary:
2025-05-25 11:11:25,381 - INFO -   Crawl4AI Health: PASS
2025-05-25 11:11:25,382 - INFO -   Crawl4AI API: PASS
2025-05-25 11:11:25,382 - INFO -   Target URL: PASS
2025-05-25 11:11:25,382 - INFO - Overall: 3/3 tests passed
2025-05-25 11:11:25,382 - INFO - SUCCESS: All tests passed! Ready to run the scraper.
2025-05-25 11:11:25,382 - INFO - STARTING: Phone information scraping (API mode)...
2025-05-25 11:11:25,390 - INFO - SUCCESS: Crawl4AI connection successful
2025-05-25 11:11:25,390 - INFO - PROCESSING: Scraping main page...
2025-05-25 11:11:25,395 - ERROR - ERROR: API request failed for http://172.18.1.16/phone/20201227.html: 422
2025-05-25 11:11:25,395 - ERROR - ERROR: Failed to scrape main page
2025-05-25 11:13:05,651 - INFO - RUNNING: Crawl4AI Health test...
2025-05-25 11:13:05,651 - INFO - Testing health endpoint: http://172.18.151.239:11235/health
2025-05-25 11:13:05,656 - INFO - SUCCESS: Crawl4AI service is healthy
2025-05-25 11:13:05,658 - INFO - PASS: Crawl4AI Health test passed
2025-05-25 11:13:05,658 - INFO - RUNNING: Crawl4AI API test...
2025-05-25 11:13:05,658 - INFO - Testing API endpoint: http://172.18.151.239:11235/crawl
2025-05-25 11:13:05,663 - INFO - SUCCESS: Crawl4AI API is working
2025-05-25 11:13:05,664 - INFO - PASS: Crawl4AI API test passed
2025-05-25 11:13:05,664 - INFO - RUNNING: Target URL test...
2025-05-25 11:13:05,664 - INFO - Testing target URL accessibility: http://172.18.1.16/phone/20201227.html
2025-05-25 11:13:05,675 - INFO - SUCCESS: Target URL is accessible
2025-05-25 11:13:05,675 - INFO - SUCCESS: Found phone-related content
2025-05-25 11:13:05,676 - INFO - PASS: Target URL test passed
2025-05-25 11:13:05,682 - INFO - SUMMARY: Test Results Summary:
2025-05-25 11:13:05,682 - INFO -   Crawl4AI Health: PASS
2025-05-25 11:13:05,682 - INFO -   Crawl4AI API: PASS
2025-05-25 11:13:05,682 - INFO -   Target URL: PASS
2025-05-25 11:13:05,683 - INFO - Overall: 3/3 tests passed
2025-05-25 11:13:05,683 - INFO - SUCCESS: All tests passed! Ready to run the scraper.
2025-05-25 11:13:05,686 - INFO - STARTING: Phone information scraping (API mode)...
2025-05-25 11:13:05,693 - INFO - SUCCESS: Crawl4AI connection successful
2025-05-25 11:13:05,693 - INFO - PROCESSING: Scraping main page...
2025-05-25 11:13:05,698 - ERROR - ERROR: API request failed for http://172.18.1.16/phone/20201227.html: 422 - {"detail":[{"type":"missing","loc":["body","urls"],"msg":"Field required","input":{"url":"http://172.18.1.16/phone/20201227.html","extraction_strategy":"NoExtractionStrategy","chunking_strategy":"RegexChunking","word_count_threshold":10,"bypass_cache":true}},{"type":"model_attributes_type","loc":["body","chunking_strategy"],"msg":"Input should be a valid dictionary or object to extract fields from","input":"RegexChunking"}]}
2025-05-25 11:13:05,698 - ERROR - ERROR: Failed to scrape main page
