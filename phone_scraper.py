#!/usr/bin/env python3
"""
Phone Information Web Scraper using Crawl4AI
Extracts phone numbers, organizational units, and contact person names
from internal network webpage: http://172.18.1.16/phone/20201227.html
"""

import asyncio
import re
import json
import csv
import logging
from typing import List, Dict, Tuple, Optional
from urllib.parse import urljoin
import aiohttp
from crawl4ai import AsyncWebCrawler
from bs4 import BeautifulSoup

# Configure logging - only to file, no console output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phone_scraper.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class PhoneScraper:
    def __init__(self, base_url: str = "http://172.18.1.16/phone/20201227.html",
                 crawl4ai_url: str = "http://172.18.151.239:11235"):
        self.base_url = base_url
        self.crawl4ai_url = crawl4ai_url
        self.api_token = "123456"
        self.extracted_data = []
        self.visited_links = set()

        # Phone number patterns
        self.phone_patterns = [
            r'\b\d{7}\b',      # 7-digit numbers
            r'\b\d{11}\b',     # 11-digit numbers
            r'\b1[3-9]\d{9}\b' # Mobile numbers
        ]

        # Exclusion patterns
        self.exclusion_keywords = [
            "4G专网手机号码", "第二人民医院急救电话", "矿消防火警电话",
            "办公室电话", "传真", "邮编"
        ]

    async def test_crawl4ai_connection(self) -> bool:
        """Test connection to crawl4ai service"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.crawl4ai_url}/health", timeout=10) as response:
                    if response.status == 200:
                        logger.info("SUCCESS: Crawl4AI connection successful")
                        return True
                    else:
                        logger.error(f"ERROR: Crawl4AI health check failed: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"ERROR: Failed to connect to Crawl4AI: {e}")
            return False

    def extract_phone_numbers(self, text: str) -> List[str]:
        """Extract phone numbers from text using regex patterns"""
        phones = []
        for pattern in self.phone_patterns:
            matches = re.findall(pattern, text)
            phones.extend(matches)
        return list(set(phones))  # Remove duplicates

    def clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ""
        # Remove extra whitespace and special characters
        text = re.sub(r'\s+', ' ', text.strip())
        text = re.sub(r'[：:]\s*', ':', text)
        return text

    def extract_contact_name(self, text: str, phone: str) -> str:
        """Extract contact person name near phone number"""
        if not text or not phone:
            return ""

        # Find phone position in text
        phone_pos = text.find(phone)
        if phone_pos == -1:
            return ""

        # Look for text before phone number
        before_text = text[:phone_pos].strip()

        # Split by common separators and get the last meaningful part
        separators = [':', '：', '、', ',', '，', ' ', '\t', '\n']
        for sep in separators:
            if sep in before_text:
                parts = before_text.split(sep)
                for part in reversed(parts):
                    part = part.strip()
                    if part and not any(keyword in part for keyword in self.exclusion_keywords):
                        # Filter out obvious non-names
                        if not re.match(r'^\d+$', part) and len(part) > 1:
                            return part

        return before_text.strip() if before_text else ""

    async def crawl_page(self, url: str) -> Optional[str]:
        """Crawl a single page using Crawl4AI"""
        try:
            async with AsyncWebCrawler(
                api_endpoint=self.crawl4ai_url,
                api_token=self.api_token,
                verbose=True
            ) as crawler:
                result = await crawler.arun(
                    url=url,
                    word_count_threshold=10,
                    extraction_strategy="NoExtractionStrategy",
                    chunking_strategy="RegexChunking",
                    bypass_cache=True
                )

                if result.success:
                    logger.info(f"SUCCESS: Successfully crawled: {url}")
                    return result.cleaned_html
                else:
                    logger.error(f"ERROR: Failed to crawl {url}: {result.error_message}")
                    return None

        except Exception as e:
            logger.error(f"ERROR: Exception while crawling {url}: {e}")
            return None

    def parse_main_page_table(self, html: str) -> List[Dict]:
        """Parse the main page direct phone table"""
        soup = BeautifulSoup(html, 'html.parser')
        extracted = []

        # Find tables containing phone numbers
        tables = soup.find_all('table')

        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                row_text = ' '.join([cell.get_text(strip=True) for cell in cells])

                # Extract phone numbers from row
                phones = self.extract_phone_numbers(row_text)

                for phone in phones:
                    contact_name = self.extract_contact_name(row_text, phone)

                    # Determine organizational unit from context
                    org_unit = "矿领导"  # Default for main page

                    # Look for organizational context in nearby text
                    for cell in cells:
                        cell_text = cell.get_text(strip=True)
                        if any(keyword in cell_text for keyword in ["矿", "厂", "部", "室", "科"]):
                            if phone not in cell_text:  # Avoid using the cell with phone
                                org_unit = cell_text
                                break

                    extracted.append({
                        'phone': phone,
                        'org_unit': org_unit,
                        'contact_name': contact_name,
                        'source_url': self.base_url
                    })

        return extracted

    def find_sub_links(self, html: str) -> List[Tuple[str, str]]:
        """Find all organizational unit sub-links"""
        soup = BeautifulSoup(html, 'html.parser')
        links = []

        # Find all links
        for link in soup.find_all('a', href=True):
            href = link['href']
            link_text = link.get_text(strip=True)

            # Skip if empty or excluded
            if not link_text or any(keyword in link_text for keyword in self.exclusion_keywords):
                continue

            # Convert relative URLs to absolute
            full_url = urljoin(self.base_url, href)

            # Filter for organizational unit links
            if any(keyword in link_text for keyword in ["厂", "部", "室", "科", "中心", "公司", "场"]):
                links.append((full_url, link_text))

        return links

    def parse_sub_page_tables(self, html: str, page_name: str) -> List[Dict]:
        """Parse tables from sub-pages with hierarchical structure"""
        soup = BeautifulSoup(html, 'html.parser')
        extracted = []

        tables = soup.find_all('table')

        for table_idx, table in enumerate(tables):
            # Try to find table header/title
            table_title = ""

            # Look for title in previous elements or parent elements
            prev_element = table.find_previous(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'div', 'p'])
            if prev_element:
                table_title = prev_element.get_text(strip=True)

            # Also check parent elements for context
            parent = table.find_parent(['div', 'section'])
            if parent and not table_title:
                title_elem = parent.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                if title_elem:
                    table_title = title_elem.get_text(strip=True)

            rows = table.find_all('tr')
            headers = []

            # Extract headers from first row
            if rows:
                header_row = rows[0]
                headers = [cell.get_text(strip=True) for cell in header_row.find_all(['th', 'td'])]

            # Process data rows
            for row_idx, row in enumerate(rows[1:] if headers else rows):
                cells = row.find_all(['td', 'th'])
                row_text = ' '.join([cell.get_text(strip=True) for cell in cells])

                # Extract all phones from the entire row first
                row_phones = self.extract_phone_numbers(row_text)

                if not row_phones:
                    continue

                # For each phone, try to find the best contact name and organizational context
                for phone in row_phones:
                    contact_name = ""

                    # Find which cell contains the phone
                    phone_cell_idx = -1
                    for col_idx, cell in enumerate(cells):
                        cell_text = cell.get_text(strip=True)
                        if phone in cell_text:
                            phone_cell_idx = col_idx
                            contact_name = self.extract_contact_name(cell_text, phone)
                            break

                    # If no contact name found in phone cell, look in adjacent cells
                    if not contact_name and phone_cell_idx >= 0:
                        # Check previous cell
                        if phone_cell_idx > 0:
                            prev_cell_text = cells[phone_cell_idx - 1].get_text(strip=True)
                            if prev_cell_text and not self.extract_phone_numbers(prev_cell_text):
                                contact_name = prev_cell_text

                        # Check next cell if still no name
                        if not contact_name and phone_cell_idx < len(cells) - 1:
                            next_cell_text = cells[phone_cell_idx + 1].get_text(strip=True)
                            if next_cell_text and not self.extract_phone_numbers(next_cell_text):
                                contact_name = next_cell_text

                    # Build hierarchical organizational unit
                    org_parts = [page_name]

                    if table_title and table_title != page_name:
                        org_parts.append(table_title)

                    # Add column header if available and meaningful
                    if (phone_cell_idx >= 0 and phone_cell_idx < len(headers) and
                        headers[phone_cell_idx] and headers[phone_cell_idx] not in contact_name):
                        org_parts.append(headers[phone_cell_idx])

                    org_unit = "-".join(filter(None, org_parts))

                    # Clean up contact name
                    contact_name = self.clean_contact_name(contact_name)

                    extracted.append({
                        'phone': phone,
                        'org_unit': org_unit,
                        'contact_name': contact_name,
                        'source_url': f"{page_name}_table_{table_idx}"
                    })

        return extracted

    def clean_contact_name(self, name: str) -> str:
        """Clean and validate contact name"""
        if not name:
            return ""

        # Remove common prefixes/suffixes
        name = re.sub(r'^(联系人|负责人|主任|经理|厂长|部长|科长|主管)[：:]?\s*', '', name)
        name = re.sub(r'\s*(联系人|负责人|主任|经理|厂长|部长|科长|主管)$', '', name)

        # Remove phone numbers from name
        for pattern in self.phone_patterns:
            name = re.sub(pattern, '', name)

        # Remove special characters
        name = re.sub(r'[：:、，,\(\)（）\[\]【】]', '', name)

        # Remove extra whitespace
        name = re.sub(r'\s+', ' ', name.strip())

        # Validate name (should be 2-4 Chinese characters typically)
        if len(name) < 2 or len(name) > 10:
            return ""

        # Check if it's likely a person's name (contains Chinese characters)
        if not re.search(r'[\u4e00-\u9fff]', name):
            return ""

        return name

    async def scrape_all_data(self) -> List[Dict]:
        """Main scraping function"""
        logger.info("STARTING: Phone information scraping...")

        # Test connection first
        if not await self.test_crawl4ai_connection():
            logger.error("ERROR: Cannot connect to Crawl4AI service. Please check the service is running.")
            return []

        # Step 1: Scrape main page
        logger.info("PROCESSING: Scraping main page...")
        main_html = await self.crawl_page(self.base_url)

        if not main_html:
            logger.error("ERROR: Failed to scrape main page")
            return []

        # Extract main page data
        main_data = self.parse_main_page_table(main_html)
        self.extracted_data.extend(main_data)
        logger.info(f"SUCCESS: Extracted {len(main_data)} records from main page")

        # Step 2: Find and scrape sub-links
        sub_links = self.find_sub_links(main_html)
        logger.info(f"FOUND: {len(sub_links)} sub-links to process")

        for url, link_text in sub_links:
            if url in self.visited_links:
                continue

            logger.info(f"PROCESSING: Scraping sub-page: {link_text}")
            sub_html = await self.crawl_page(url)

            if sub_html:
                sub_data = self.parse_sub_page_tables(sub_html, link_text)
                self.extracted_data.extend(sub_data)
                logger.info(f"SUCCESS: Extracted {len(sub_data)} records from {link_text}")

            self.visited_links.add(url)

            # Add delay to avoid overwhelming the server
            await asyncio.sleep(1)

        logger.info(f"COMPLETED: Scraping completed! Total records: {len(self.extracted_data)}")
        return self.extracted_data

    def save_to_csv(self, filename: str = "phone_data.csv"):
        """Save extracted data to CSV file"""
        if not self.extracted_data:
            logger.warning("WARNING: No data to save")
            return

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['phone', 'org_unit', 'contact_name', 'source_url']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for record in self.extracted_data:
                writer.writerow(record)

        logger.info(f"SAVED: Data saved to {filename}")

    def save_to_json(self, filename: str = "phone_data.json"):
        """Save extracted data to JSON file"""
        if not self.extracted_data:
            logger.warning("WARNING: No data to save")
            return

        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.extracted_data, jsonfile, ensure_ascii=False, indent=2)

        logger.info(f"SAVED: Data saved to {filename}")

    def print_summary(self):
        """Print extraction summary"""
        if not self.extracted_data:
            print("ERROR: No data extracted")
            return

        print(f"\nSUMMARY: Extraction Summary:")
        print(f"Total records: {len(self.extracted_data)}")

        # Group by organizational unit
        org_counts = {}
        for record in self.extracted_data:
            org = record['org_unit']
            org_counts[org] = org_counts.get(org, 0) + 1

        print(f"Organizational units: {len(org_counts)}")

        # Show sample data
        print(f"\nSAMPLE: Sample Records:")
        for i, record in enumerate(self.extracted_data[:5]):
            print(f"{i+1}. {record['phone']}, {record['org_unit']}, {record['contact_name']}")

        if len(self.extracted_data) > 5:
            print(f"... and {len(self.extracted_data) - 5} more records")


async def main():
    """Main execution function"""
    scraper = PhoneScraper()

    try:
        # Run the scraping process
        data = await scraper.scrape_all_data()

        if data:
            # Save results
            scraper.save_to_csv()
            scraper.save_to_json()
            scraper.print_summary()
        else:
            logger.error("ERROR: No data was extracted")

    except Exception as e:
        logger.error(f"ERROR: Scraping failed with error: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
