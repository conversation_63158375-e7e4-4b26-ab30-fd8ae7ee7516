#!/usr/bin/env python3
"""
Firecrawl爬虫演示版本 - 不需要API密钥
使用模拟的HTML数据演示Firecrawl爬虫的工作流程
"""

import re
import json
import csv
import logging
from typing import List, Dict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('E:/mycode/pc1/firecrawl/phone_scraper_demo.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class PhoneScraperFirecrawlDemo:
    """
    Firecrawl爬虫演示类 - 使用模拟数据
    
    演示Firecrawl爬虫的完整工作流程，包括：
    1. 模拟Firecrawl API调用
    2. HTML内容解析
    3. 电话数据提取
    4. 结果保存
    """
    
    def __init__(self):
        """初始化演示爬虫"""
        self.target_url = "http://172.18.1.16/phone/20201227.html"
        self.phone_data = []
        self.phone_pattern = r'\b\d{7,11}\b'
        
        # 模拟的HTML内容 - 基于实际网页结构
        self.mock_html = """
        <html>
        <head><title>矿领导电话表格</title></head>
        <body>
            <h1>矿领导办公电话</h1>
            <table border="1">
                <tr>
                    <th>姓名</th>
                    <th>办公室电话</th>
                    <th>姓名</th>
                    <th>办公室电话</th>
                    <th>姓名</th>
                    <th>办公室电话</th>
                </tr>
                <tr>
                    <td>吴启明</td>
                    <td>7719001</td>
                    <td>潘斌</td>
                    <td>7716398</td>
                    <td>耿志强</td>
                    <td>7716109</td>
                </tr>
                <tr>
                    <td>李建国</td>
                    <td>7719006</td>
                    <td>周云</td>
                    <td>7719016</td>
                    <td>周宏</td>
                    <td>7716399</td>
                </tr>
                <tr>
                    <td>曾流生</td>
                    <td>7719998</td>
                    <td>王志强</td>
                    <td>7716396</td>
                    <td>张林水</td>
                    <td>7719025</td>
                </tr>
                <tr>
                    <td>曾芳</td>
                    <td>7719020</td>
                    <td>黄满武</td>
                    <td>7719018</td>
                    <td></td>
                    <td></td>
                </tr>
            </table>
        </body>
        </html>
        """
    
    def simulate_firecrawl_scraping(self) -> str:
        """
        模拟Firecrawl爬取过程
        
        Returns:
            str: 模拟的HTML内容
        """
        logger.info(f"INFO: 模拟Firecrawl爬取: {self.target_url}")
        print(f"🔍 INFO: 模拟Firecrawl爬取目标网页...")
        print(f"📄 INFO: 目标URL: {self.target_url}")
        
        # 模拟网络延迟
        import time
        time.sleep(1)
        
        logger.info("SUCCESS: 模拟Firecrawl爬取成功")
        print("✅ SUCCESS: 模拟Firecrawl爬取成功")
        print(f"📄 INFO: 获取内容长度: {len(self.mock_html)} 字符")
        
        return self.mock_html
    
    def extract_phone_data(self, html_content: str) -> List[Dict]:
        """
        从HTML内容中提取电话数据
        
        Args:
            html_content: 网页HTML内容
            
        Returns:
            List[Dict]: 提取的电话数据列表
        """
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(html_content, 'html.parser')
        extracted = []
        
        logger.info("INFO: 开始解析HTML内容提取电话数据")
        print("🔍 INFO: 开始解析HTML内容提取电话数据")
        
        # 查找表格
        tables = soup.find_all('table')
        logger.info(f"INFO: 找到 {len(tables)} 个表格")
        print(f"📊 INFO: 找到 {len(tables)} 个表格")
        
        for table_idx, table in enumerate(tables):
            table_text = table.get_text()
            
            # 检查是否是目标表格
            if "电话" in table_text or "姓名" in table_text:
                logger.info(f"FOUND: 在第{table_idx+1}个表格中找到目标表格")
                print(f"✅ FOUND: 在第{table_idx+1}个表格中找到目标表格")
                
                rows = table.find_all('tr')
                
                for row_idx, row in enumerate(rows):
                    cells = row.find_all(['td', 'th'])
                    
                    if len(cells) < 2:
                        continue
                    
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    row_text = ' '.join(cell_texts)
                    
                    # 查找电话号码
                    phones = re.findall(self.phone_pattern, row_text)
                    
                    if phones:
                        logger.info(f"PROCESSING: 第{row_idx+1}行找到电话: {phones}")
                        print(f"📞 PROCESSING: 第{row_idx+1}行找到电话: {phones}")
                        
                        for phone in phones:
                            # 查找对应的姓名
                            name = ""
                            phone_cell_idx = -1
                            
                            for idx, cell_text in enumerate(cell_texts):
                                if phone in cell_text:
                                    phone_cell_idx = idx
                                    break
                            
                            # 根据位置推断姓名
                            if phone_cell_idx > 0:
                                name = cell_texts[phone_cell_idx - 1]
                            elif phone_cell_idx == 0 and len(cell_texts) > 1:
                                name = cell_texts[1]
                            
                            # 清理姓名
                            name = self.clean_name(name)
                            
                            if self.is_valid_phone(phone) and name:
                                record = {
                                    'phone': phone,
                                    'unit': '矿领导',
                                    'name': name,
                                    'extraction_method': 'firecrawl_demo'
                                }
                                extracted.append(record)
                                logger.info(f"EXTRACTED: {phone}、矿领导、{name}")
                                print(f"✅ EXTRACTED: {phone}、矿领导、{name}")
                
                break
        
        return extracted
    
    def clean_name(self, name: str) -> str:
        """清理姓名"""
        if not name:
            return ""
        
        name = re.sub(r'(姓名|联系人|负责人|主任|经理|厂长|部长|科长)', '', name)
        name = re.sub(r'[：:、，,\(\)（）\[\]【】]', '', name)
        name = re.sub(r'\d+', '', name)
        name = name.strip()
        
        if 2 <= len(name) <= 4 and re.match(r'^[\u4e00-\u9fff]+$', name):
            return name
        return ""
    
    def is_valid_phone(self, phone: str) -> bool:
        """验证电话号码"""
        return len(phone) == 7 or len(phone) == 11
    
    def save_results(self):
        """保存结果"""
        # 保存CSV文件
        csv_file = 'E:/mycode/pc1/firecrawl/phone_data_demo.csv'
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['电话号码', '电话所在单位', '用户名']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in self.phone_data:
                writer.writerow({
                    '电话号码': record['phone'],
                    '电话所在单位': record['unit'],
                    '用户名': record['name']
                })
        
        # 保存JSON文件
        json_file = 'E:/mycode/pc1/firecrawl/phone_data_demo.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.phone_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"SAVED: 演示数据已保存")
        print(f"\n💾 数据已保存到:")
        print(f"  - phone_data_demo.csv (中文列名)")
        print(f"  - phone_data_demo.json (完整数据)")
        print(f"  - phone_scraper_demo.log (详细日志)")
    
    def run_demo(self):
        """运行演示"""
        print("🎭 Firecrawl爬虫演示版本")
        print("=" * 60)
        print("📝 注意: 这是演示版本，使用模拟数据展示Firecrawl爬虫工作流程")
        print("🔑 要使用真实的Firecrawl服务，请设置FIRECRAWL_API_KEY环境变量")
        print("=" * 60)
        
        # 第一步：模拟Firecrawl爬取
        html_content = self.simulate_firecrawl_scraping()
        
        # 第二步：提取电话数据
        self.phone_data = self.extract_phone_data(html_content)
        
        # 第三步：显示结果
        if self.phone_data:
            print(f"\n🎉 成功提取 {len(self.phone_data)} 条电话记录:")
            print("=" * 60)
            
            for i, record in enumerate(self.phone_data, 1):
                print(f"{i:2d}. {record['phone']}、{record['unit']}、{record['name']}")
            
            # 第四步：保存数据
            self.save_results()
            
            # 按要求格式输出
            print("=" * 60)
            print("📋 按要求格式输出:")
            formatted = []
            for record in self.phone_data:
                formatted.append(f"{record['phone']}、{record['unit']}、{record['name']}")
            print("; ".join(formatted))
            
            print("\n🎯 演示完成！")
            print("💡 提示: 要使用真实的Firecrawl服务爬取实际网页，请:")
            print("   1. 获取Firecrawl API密钥")
            print("   2. 设置环境变量: set FIRECRAWL_API_KEY=your_api_key")
            print("   3. 运行: python phone_scraper_firecrawl.py")
            
        else:
            print("❌ ERROR: 演示数据提取失败")

def main():
    """主函数"""
    demo = PhoneScraperFirecrawlDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()
