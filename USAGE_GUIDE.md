# 📞 Phone Information Scraper - Quick Start Guide

## 🚀 Quick Setup & Execution

### Step 1: Environment Setup
```bash
# 1. Start Crawl4AI Docker service
docker run --rm -it -e CRAWL4AI_API_TOKEN=123456 -p 11235:11235 unclecode/crawl4ai:all-amd64

# 2. Install dependencies (in a new terminal)
pip install -r requirements.txt
```

### Step 2: Run the Scraper
```bash
# Option A: Run with tests first (recommended)
python run_scraper.py --test

# Option B: Run directly
python run_scraper.py

# Option C: Test connection only
python test_connection.py
```

### Step 3: Analyze Results
```bash
# Analyze extracted data
python analyze_data.py
```

## 📁 Output Files

After successful execution, you'll find:
- `phone_data.csv` - Structured data in CSV format
- `phone_data.json` - JSON format for programmatic use
- `phone_scraper.log` - Detailed execution logs
- `analysis_report.txt` - Data analysis summary

## 🎯 Expected Results

The scraper will extract approximately:
- **Main page**: 11 direct phone numbers
- **Sub-pages**: ~42 organizational units with hundreds of phone numbers
- **Total**: 200-500+ phone records (depending on webpage content)

### Sample Output Format:
```
Phone Number, Organizational Unit, Contact Person
7719001, 矿领导, 吴启明
7719218, 采矿场-场机关-场领导, 场长
18046626265, 大山选矿厂-厂机关-供应室, 李建国
```

## 🔧 Configuration

### Target Settings (config.py)
- **Base URL**: `http://***********/phone/20201227.html`
- **Crawl4AI URL**: `http://172.18.151.239:11235`
- **API Token**: `123456`

### Customization Options
- **Request delay**: Adjust `REQUEST_DELAY` for slower/faster scraping
- **Phone patterns**: Modify `PHONE_PATTERNS` for different number formats
- **Exclusions**: Update `EXCLUSION_KEYWORDS` to filter unwanted content

## 🐛 Troubleshooting

### Common Issues & Solutions

#### 1. Connection Failed
```
❌ Failed to connect to Crawl4AI
```
**Solution**: 
- Check Docker service: `docker ps`
- Restart container if needed
- Verify port 11235 is not blocked

#### 2. No Data Extracted
```
❌ No data was extracted
```
**Solution**:
- Verify target URL is accessible: `curl http://***********/phone/20201227.html`
- Check network connectivity to internal server
- Review logs for specific errors

#### 3. Partial Results
```
⚠️ Some sub-links failed to scrape
```
**Solution**:
- Increase `REQUEST_DELAY` in config.py
- Check individual sub-link accessibility
- Review server rate limiting

#### 4. Import Errors
```
ModuleNotFoundError: No module named 'crawl4ai'
```
**Solution**:
```bash
pip install crawl4ai aiohttp beautifulsoup4 lxml
```

## 📊 Data Quality Checks

### Validation Steps
1. **Phone Number Format**: 7 or 11 digits
2. **Contact Names**: Chinese characters, 2-10 characters
3. **Organizational Units**: Hierarchical structure with '-' separators
4. **Duplicates**: Automatic detection and reporting

### Quality Metrics
- **Completeness**: % of records with all fields populated
- **Accuracy**: Phone number format validation
- **Consistency**: Organizational unit naming conventions

## 🔄 Advanced Usage

### Custom Scraping
```python
from phone_scraper import PhoneScraper
import asyncio

async def custom_scrape():
    scraper = PhoneScraper(
        base_url="your_custom_url",
        crawl4ai_url="your_crawl4ai_endpoint"
    )
    
    # Test connection first
    if await scraper.test_crawl4ai_connection():
        data = await scraper.scrape_all_data()
        return data
    return []

# Execute
results = asyncio.run(custom_scrape())
```

### Batch Processing
```python
# Process multiple URLs
urls = [
    "http://***********/phone/20201227.html",
    "http://***********/phone/other_page.html"
]

for url in urls:
    scraper = PhoneScraper(base_url=url)
    data = await scraper.scrape_all_data()
    scraper.save_to_csv(f"data_{url.split('/')[-1]}.csv")
```

## 📈 Performance Optimization

### Speed Improvements
- **Parallel processing**: Modify scraper for concurrent sub-link processing
- **Caching**: Enable result caching for repeated runs
- **Selective scraping**: Target specific organizational units only

### Resource Management
- **Memory**: Monitor for large datasets (>1000 records)
- **Network**: Implement exponential backoff for retries
- **Storage**: Compress output files for large datasets

## 🔒 Security Considerations

- **Internal network only**: Scraper designed for internal use
- **Rate limiting**: Built-in delays to avoid server overload
- **Error handling**: Graceful failure without exposing sensitive data
- **Logging**: Detailed logs for audit trails

## 📞 Support

For issues or questions:
1. Check logs in `phone_scraper.log`
2. Run connection tests: `python test_connection.py`
3. Verify configuration in `config.py`
4. Review this guide for common solutions

## 🎉 Success Indicators

You'll know the scraper is working correctly when you see:
- ✅ All connection tests pass
- ✅ Main page data extracted (11+ records)
- ✅ Sub-links processed successfully (~42 links)
- ✅ Output files generated with data
- ✅ Analysis report shows reasonable data distribution

**Happy scraping! 🕷️📞**
