#!/usr/bin/env python3
"""
专门爬取矿领导电话表格的爬虫程序
目标：http://172.18.1.16/phone/20201227.html 中的矿领导电话表格
格式：电话号码、所在单位、用户名
"""

import asyncio
import re
import json
import csv
import logging
from typing import List, Dict, Optional
import aiohttp
from bs4 import BeautifulSoup

# 配置日志 - 只输出到文件
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phone_table_scraper.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class PhoneTableScraper:
    def __init__(self, target_url: str = "http://172.18.1.16/phone/20201227.html"):
        self.target_url = target_url
        self.phone_data = []
        
        # 电话号码匹配模式
        self.phone_pattern = r'\b\d{7,11}\b'
    
    async def test_connection(self) -> bool:
        """测试目标网页连接"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.target_url, timeout=10) as response:
                    if response.status == 200:
                        logger.info(f"SUCCESS: 成功连接到目标网页: {self.target_url}")
                        return True
                    else:
                        logger.error(f"ERROR: 网页返回状态码: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"ERROR: 连接失败: {e}")
            return False
    
    async def fetch_page(self) -> Optional[str]:
        """获取网页内容"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.target_url, timeout=30) as response:
                    if response.status == 200:
                        # 尝试不同编码
                        try:
                            content = await response.text(encoding='utf-8')
                        except UnicodeDecodeError:
                            try:
                                content = await response.text(encoding='gbk')
                            except UnicodeDecodeError:
                                content = await response.text(encoding='gb2312')
                        
                        logger.info("SUCCESS: 成功获取网页内容")
                        return content
                    else:
                        logger.error(f"ERROR: 获取网页失败，状态码: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"ERROR: 获取网页异常: {e}")
            return None
    
    def extract_phone_table(self, html: str) -> List[Dict]:
        """提取矿领导电话表格数据"""
        soup = BeautifulSoup(html, 'html.parser')
        extracted_data = []
        
        # 查找包含"矿领导"的表格
        tables = soup.find_all('table')
        
        for table in tables:
            # 检查表格是否包含矿领导相关内容
            table_text = table.get_text()
            if "矿领导" in table_text or "姓名" in table_text or "办公室电话" in table_text:
                logger.info("FOUND: 找到矿领导电话表格")
                
                rows = table.find_all('tr')
                
                for row_idx, row in enumerate(rows):
                    cells = row.find_all(['td', 'th'])
                    
                    # 跳过表头行
                    if len(cells) < 2:
                        continue
                    
                    # 提取每行的所有文本
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    row_text = ' '.join(cell_texts)
                    
                    # 查找电话号码
                    phones = re.findall(self.phone_pattern, row_text)
                    
                    if phones:
                        logger.info(f"PROCESSING: 处理第{row_idx+1}行，找到电话: {phones}")
                        
                        # 对每个电话号码进行处理
                        for phone in phones:
                            # 查找电话号码所在的单元格位置
                            phone_cell_idx = -1
                            for idx, cell_text in enumerate(cell_texts):
                                if phone in cell_text:
                                    phone_cell_idx = idx
                                    break
                            
                            # 提取姓名（通常在电话号码前面的单元格）
                            name = ""
                            if phone_cell_idx > 0:
                                # 从前一个单元格获取姓名
                                name = cell_texts[phone_cell_idx - 1]
                            elif phone_cell_idx == 0 and len(cell_texts) > 1:
                                # 如果电话在第一列，姓名可能在第二列
                                name = cell_texts[1]
                            
                            # 清理姓名
                            name = self.clean_name(name)
                            
                            # 确定所在单位
                            unit = "矿领导"  # 根据您的需求，这个表格都是矿领导
                            
                            # 验证数据有效性
                            if self.is_valid_phone(phone) and name:
                                phone_record = {
                                    'phone': phone,
                                    'unit': unit,
                                    'name': name,
                                    'row_data': row_text  # 保存原始行数据用于调试
                                }
                                extracted_data.append(phone_record)
                                logger.info(f"EXTRACTED: {phone}、{unit}、{name}")
                
                break  # 找到目标表格后退出循环
        
        return extracted_data
    
    def clean_name(self, name: str) -> str:
        """清理姓名数据"""
        if not name:
            return ""
        
        # 移除常见的非姓名文本
        name = re.sub(r'(姓名|联系人|负责人|主任|经理|厂长|部长|科长)', '', name)
        name = re.sub(r'[：:、，,\(\)（）\[\]【】]', '', name)
        name = re.sub(r'\d+', '', name)  # 移除数字
        name = name.strip()
        
        # 验证是否为有效姓名（2-4个中文字符）
        if 2 <= len(name) <= 4 and re.match(r'^[\u4e00-\u9fff]+$', name):
            return name
        
        return ""
    
    def is_valid_phone(self, phone: str) -> bool:
        """验证电话号码有效性"""
        # 7位内线电话或11位手机号码
        return len(phone) == 7 or len(phone) == 11
    
    async def scrape_phone_table(self) -> List[Dict]:
        """主要爬取函数"""
        logger.info("START: 开始爬取矿领导电话表格")
        
        # 测试连接
        if not await self.test_connection():
            logger.error("ERROR: 无法连接到目标网页")
            return []
        
        # 获取网页内容
        html_content = await self.fetch_page()
        if not html_content:
            logger.error("ERROR: 无法获取网页内容")
            return []
        
        # 提取表格数据
        self.phone_data = self.extract_phone_table(html_content)
        
        logger.info(f"COMPLETED: 爬取完成，共提取 {len(self.phone_data)} 条电话记录")
        return self.phone_data
    
    def save_to_csv(self, filename: str = "phone_table_data.csv"):
        """保存数据到CSV文件"""
        if not self.phone_data:
            logger.warning("WARNING: 没有数据可保存")
            return
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['phone', 'unit', 'name']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for record in self.phone_data:
                writer.writerow({
                    'phone': record['phone'],
                    'unit': record['unit'],
                    'name': record['name']
                })
        
        logger.info(f"SAVED: 数据已保存到 {filename}")
    
    def save_to_json(self, filename: str = "phone_table_data.json"):
        """保存数据到JSON文件"""
        if not self.phone_data:
            logger.warning("WARNING: 没有数据可保存")
            return
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.phone_data, jsonfile, ensure_ascii=False, indent=2)
        
        logger.info(f"SAVED: 数据已保存到 {filename}")
    
    def print_results(self):
        """打印结果"""
        if not self.phone_data:
            print("ERROR: 没有提取到数据")
            return
        
        print(f"\n矿领导电话表格数据 (共 {len(self.phone_data)} 条记录):")
        print("=" * 60)
        
        for i, record in enumerate(self.phone_data, 1):
            print(f"{i:2d}. {record['phone']}、{record['unit']}、{record['name']}")
        
        print("=" * 60)
        
        # 按您要求的格式输出
        print("\n按要求格式输出:")
        formatted_output = []
        for record in self.phone_data:
            formatted_output.append(f"{record['phone']}、{record['unit']}、{record['name']}")
        
        print("; ".join(formatted_output))


async def main():
    """主函数"""
    print("矿领导电话表格爬虫")
    print("=" * 40)
    
    scraper = PhoneTableScraper()
    
    try:
        # 执行爬取
        data = await scraper.scrape_phone_table()
        
        if data:
            # 显示结果
            scraper.print_results()
            
            # 保存数据
            scraper.save_to_csv()
            scraper.save_to_json()
            
            print(f"\n数据文件已保存:")
            print(f"  - phone_table_data.csv")
            print(f"  - phone_table_data.json")
            print(f"  - phone_table_scraper.log")
        else:
            print("ERROR: 没有提取到任何数据，请检查日志文件")
    
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"ERROR: 程序执行失败: {e}")
        logger.error(f"ERROR: 程序执行失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
