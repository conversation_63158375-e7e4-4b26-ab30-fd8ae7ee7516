#!/usr/bin/env python3
"""
使用crawl4ai技术识别网页表格结构和子链接的爬虫
目标：http://172.18.1.16/phone/20201227.html
功能：识别5个表格+1个项目经理部，提取单位名称和对应的子链接
"""

import os
import re
import json
import csv
import logging
import asyncio
import aiohttp
from typing import List, Dict, Optional, Tuple
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('newcraper/table_link_scraper_crawl4ai.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class TableLinkScraperCrawl4AI:
    """
    使用crawl4ai的表格链接识别爬虫类

    主要功能：
    1. 连接本地crawl4ai服务
    2. 爬取指定网页的表格结构
    3. 识别表格名称和单位名称
    4. 提取单位对应的子链接
    5. 构建层次化的单位结构数据
    """

    def __init__(self):
        """初始化爬虫配置参数"""
        # 目标网页地址
        self.target_url = "http://172.18.1.16/phone/20201227.html"

        # 本地部署的crawl4ai服务地址
        self.crawl4ai_url = "http://172.18.151.239:11235"

        # 存储提取的表格和链接数据
        self.table_data = []

        # 预期的表格结构
        self.expected_tables = {
            "机关部室办公电话": 21,
            "主要生产单位办公电话": 8,
            "主要经营单位办公电话": 3,
            "辅助生产单位办公电话": 6,
            "主要服务单位办公电话": 2,
            "项目经理部": 1
        }

    async def test_crawl4ai(self) -> bool:
        """
        测试crawl4ai服务连接状态

        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.crawl4ai_url}/health", timeout=10) as response:
                    if response.status == 200:
                        logger.info("SUCCESS: crawl4ai服务连接成功")
                        print("SUCCESS: crawl4ai服务连接成功")
                        return True
                    else:
                        logger.error(f"ERROR: crawl4ai连接失败: {response.status}")
                        print(f"ERROR: crawl4ai连接失败: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"ERROR: crawl4ai连接异常: {e}")
            print(f"ERROR: crawl4ai连接异常: {e}")
            return False

    async def crawl_with_post_api(self) -> Optional[str]:
        """
        使用crawl4ai的POST API进行网页爬取

        Returns:
            Optional[str]: 成功返回HTML内容，失败返回None
        """
        try:
            async with aiohttp.ClientSession() as session:
                api_url = f"{self.crawl4ai_url}/crawl"

                # 构建请求载荷
                payload = {
                    'urls': [self.target_url]
                }

                # 设置请求头
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer 123456'
                }

                logger.info(f"INFO: 使用crawl4ai爬取目标网页")
                print(f"INFO: 使用crawl4ai爬取目标网页...")

                async with session.post(api_url, json=payload, headers=headers, timeout=60) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"INFO: POST API响应键: {list(result.keys())}")

                        if 'task_id' in result:
                            logger.info(f"INFO: 获得任务ID: {result['task_id']}")
                            print(f"INFO: 获得任务ID: {result['task_id']}")
                            return await self.wait_for_task(session, result['task_id'])

                        elif result.get('results'):
                            first_result = result['results'][0]
                            html_content = (first_result.get('cleaned_html') or
                                          first_result.get('raw_html') or
                                          first_result.get('html', ''))
                            if html_content:
                                logger.info("SUCCESS: POST API获取内容成功")
                                print("SUCCESS: POST API获取内容成功")
                                return html_content

                        logger.error("ERROR: POST API无法获取内容")
                        print("ERROR: POST API无法获取内容")
                        return None
                    else:
                        error_text = await response.text()
                        logger.error(f"ERROR: POST API失败: {response.status} - {error_text}")
                        print(f"ERROR: POST API失败: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"ERROR: POST API异常: {e}")
            print(f"ERROR: POST API异常: {e}")
            return None

    async def wait_for_task(self, session: aiohttp.ClientSession, task_id: str) -> Optional[str]:
        """
        等待crawl4ai异步任务完成并获取结果

        Args:
            session: HTTP会话对象
            task_id: crawl4ai返回的任务ID

        Returns:
            Optional[str]: 成功返回HTML内容，失败返回None
        """
        try:
            for attempt in range(20):
                await asyncio.sleep(3)

                status_url = f"{self.crawl4ai_url}/task/{task_id}"
                logger.info(f"INFO: 检查任务状态 (尝试 {attempt + 1}/20)")
                print(f"INFO: 检查任务状态 (尝试 {attempt + 1}/20)")

                headers = {'Authorization': 'Bearer 123456'}

                async with session.get(status_url, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        result = await response.json()
                        status = result.get('status', 'unknown')

                        logger.info(f"INFO: 任务状态: {status}")
                        print(f"INFO: 任务状态: {status}")

                        if status == 'completed':
                            logger.info(f"DEBUG: 完成任务的结果键: {list(result.keys())}")

                            # 尝试不同的结果路径
                            html_content = None

                            if result.get('results'):
                                first_result = result['results'][0]
                                html_content = (first_result.get('cleaned_html') or
                                              first_result.get('raw_html') or
                                              first_result.get('html', ''))

                            elif result.get('result') and result['result'].get('results'):
                                first_result = result['result']['results'][0]
                                html_content = (first_result.get('cleaned_html') or
                                              first_result.get('raw_html') or
                                              first_result.get('html', ''))

                            elif result.get('cleaned_html') or result.get('raw_html') or result.get('html'):
                                html_content = (result.get('cleaned_html') or
                                              result.get('raw_html') or
                                              result.get('html', ''))

                            if html_content:
                                logger.info("SUCCESS: 异步任务完成，获取内容成功")
                                print("SUCCESS: 异步任务完成，获取内容成功")
                                return html_content
                            else:
                                logger.error(f"ERROR: 任务完成但无法找到HTML内容")
                                print(f"ERROR: 任务完成但无法找到HTML内容")

                        elif status == 'failed':
                            logger.error(f"ERROR: 异步任务失败: {result.get('error', 'Unknown')}")
                            print(f"ERROR: 异步任务失败")
                            return None
                    else:
                        logger.warning(f"WARNING: 无法获取任务状态: {response.status}")
                        print(f"WARNING: 无法获取任务状态: {response.status}")

            logger.error("ERROR: 异步任务超时")
            print("ERROR: 异步任务超时")
            return None

        except Exception as e:
            logger.error(f"ERROR: 等待任务异常: {e}")
            print(f"ERROR: 等待任务异常: {e}")
            return None

    def extract_tables_and_links(self, html_content: str) -> List[Dict]:
        """
        从HTML内容中提取表格结构和链接信息

        Args:
            html_content: 网页HTML内容

        Returns:
            List[Dict]: 提取的表格和链接数据列表
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        extracted_data = []

        logger.info("INFO: 开始解析HTML内容，识别表格和链接")
        print("INFO: 开始解析HTML内容，识别表格和链接")

        # 查找所有表格
        tables = soup.find_all('table')
        logger.info(f"INFO: 找到 {len(tables)} 个表格")
        print(f"INFO: 找到 {len(tables)} 个表格")

        # 查找表格标题（通常在表格前面的文本或标题标签中）
        table_titles = self.find_table_titles(soup)
        logger.info(f"INFO: 识别到 {len(table_titles)} 个表格标题")
        print(f"INFO: 识别到 {len(table_titles)} 个表格标题")

        # 遍历每个表格
        for table_idx, table in enumerate(tables):
            # 获取表格标题
            table_title = self.get_table_title(table, table_titles, table_idx)

            logger.info(f"PROCESSING: 处理第{table_idx+1}个表格: {table_title}")
            print(f"PROCESSING: 处理第{table_idx+1}个表格: {table_title}")

            # 提取表格中的单位和链接
            units_and_links = self.extract_units_from_table(table, table_title)

            if units_and_links:
                table_data = {
                    'table_index': table_idx + 1,
                    'table_title': table_title,
                    'unit_count': len(units_and_links),
                    'units': units_and_links
                }
                extracted_data.append(table_data)

                logger.info(f"EXTRACTED: 表格 '{table_title}' 包含 {len(units_and_links)} 个单位")
                print(f"EXTRACTED: 表格 '{table_title}' 包含 {len(units_and_links)} 个单位")

        # 查找项目经理部（可能不在表格中）
        project_manager_data = self.find_project_manager_section(soup)
        if project_manager_data:
            extracted_data.append(project_manager_data)

        return extracted_data

    def find_table_titles(self, soup: BeautifulSoup) -> List[str]:
        """
        查找页面中的表格标题

        Args:
            soup: BeautifulSoup解析对象

        Returns:
            List[str]: 表格标题列表
        """
        titles = []

        # 查找可能的标题模式
        title_patterns = [
            r'.*办公电话',
            r'.*经理部',
            r'.*部室.*',
            r'.*单位.*'
        ]

        # 在各种标签中查找标题
        for tag in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div', 'span', 'td', 'th']):
            text = tag.get_text(strip=True)
            if text:
                for pattern in title_patterns:
                    if re.match(pattern, text):
                        titles.append(text)
                        break

        # 去重并保持顺序
        unique_titles = []
        for title in titles:
            if title not in unique_titles:
                unique_titles.append(title)

        return unique_titles

    def get_table_title(self, table, table_titles: List[str], table_idx: int) -> str:
        """
        获取表格的标题

        Args:
            table: 表格元素
            table_titles: 已识别的标题列表
            table_idx: 表格索引

        Returns:
            str: 表格标题
        """
        # 方法1: 查找表格前面的标题
        prev_element = table.find_previous_sibling()
        while prev_element:
            if prev_element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div']:
                text = prev_element.get_text(strip=True)
                if text and any(keyword in text for keyword in ['办公电话', '经理部', '部室', '单位']):
                    return text
            prev_element = prev_element.find_previous_sibling()

        # 方法2: 查找表格内的标题行
        first_row = table.find('tr')
        if first_row:
            cells = first_row.find_all(['td', 'th'])
            if len(cells) == 1:  # 可能是标题行
                title_text = cells[0].get_text(strip=True)
                if title_text and any(keyword in title_text for keyword in ['办公电话', '经理部', '部室', '单位']):
                    return title_text

        # 方法3: 从预识别的标题中匹配
        if table_idx < len(table_titles):
            return table_titles[table_idx]

        # 方法4: 根据表格内容推断
        table_text = table.get_text()
        if '机关' in table_text or '部室' in table_text:
            return "机关部室办公电话"
        elif '生产单位' in table_text and '主要' in table_text:
            return "主要生产单位办公电话"
        elif '经营单位' in table_text:
            return "主要经营单位办公电话"
        elif '辅助' in table_text and '生产' in table_text:
            return "辅助生产单位办公电话"
        elif '服务单位' in table_text:
            return "主要服务单位办公电话"

        # 默认标题
        return f"表格{table_idx + 1}"

    def extract_units_from_table(self, table, table_title: str) -> List[Dict]:
        """
        从表格中提取单位名称和链接

        Args:
            table: 表格元素
            table_title: 表格标题

        Returns:
            List[Dict]: 单位和链接信息列表
        """
        units = []

        # 获取表格中的所有行
        rows = table.find_all('tr')

        for row_idx, row in enumerate(rows):
            cells = row.find_all(['td', 'th'])

            for cell_idx, cell in enumerate(cells):
                # 获取单元格文本
                cell_text = cell.get_text(strip=True)

                # 跳过空单元格和明显的标题单元格
                if not cell_text or cell_text in ['姓名', '电话', '办公电话', '联系电话']:
                    continue

                # 查找链接
                links = cell.find_all('a', href=True)

                if links:
                    # 有链接的单元格
                    for link in links:
                        link_text = link.get_text(strip=True)
                        link_url = link.get('href')

                        if link_text and link_url:
                            # 构建完整URL
                            full_url = urljoin(self.target_url, link_url)

                            unit_info = {
                                'unit_name': link_text,
                                'unit_link': full_url,
                                'table_title': table_title,
                                'row_index': row_idx + 1,
                                'cell_index': cell_idx + 1,
                                'has_link': True
                            }
                            units.append(unit_info)

                            logger.info(f"FOUND_LINK: {link_text} -> {full_url}")
                            print(f"FOUND_LINK: {link_text} -> {full_url}")

                elif cell_text and self.is_unit_name(cell_text):
                    # 没有链接但看起来是单位名称的单元格
                    unit_info = {
                        'unit_name': cell_text,
                        'unit_link': None,
                        'table_title': table_title,
                        'row_index': row_idx + 1,
                        'cell_index': cell_idx + 1,
                        'has_link': False
                    }
                    units.append(unit_info)

                    logger.info(f"FOUND_UNIT: {cell_text} (无链接)")
                    print(f"FOUND_UNIT: {cell_text} (无链接)")

        return units

    def is_unit_name(self, text: str) -> bool:
        """
        判断文本是否可能是单位名称

        Args:
            text: 待判断的文本

        Returns:
            bool: 是单位名称返回True
        """
        # 单位名称的特征
        unit_keywords = [
            '部', '室', '科', '处', '厂', '公司', '中心', '站', '队', '组',
            '办', '局', '院', '所', '馆', '库', '场', '园', '区'
        ]

        # 排除的关键词
        exclude_keywords = [
            '电话', '姓名', '联系', '负责人', '主任', '经理', '厂长',
            '部长', '科长', '处长', '主管', '专员'
        ]

        # 检查长度
        if len(text) < 2 or len(text) > 20:
            return False

        # 检查是否包含排除关键词
        if any(keyword in text for keyword in exclude_keywords):
            return False

        # 检查是否包含单位关键词
        if any(keyword in text for keyword in unit_keywords):
            return True

        # 检查是否是纯数字（电话号码）
        if re.match(r'^\d+$', text):
            return False

        # 检查是否是人名模式（2-4个中文字符且不包含单位关键词）
        if re.match(r'^[\u4e00-\u9fff]{2,4}$', text) and not any(keyword in text for keyword in unit_keywords):
            return False

        return True

    def find_project_manager_section(self, soup: BeautifulSoup) -> Optional[Dict]:
        """
        查找项目经理部相关信息

        Args:
            soup: BeautifulSoup解析对象

        Returns:
            Optional[Dict]: 项目经理部信息，如果找到的话
        """
        # 查找包含"项目经理部"的文本
        for tag in soup.find_all(text=re.compile(r'.*项目经理部.*')):
            parent = tag.parent
            if parent:
                # 查找附近的链接
                links = []

                # 在父元素及其兄弟元素中查找链接
                for sibling in parent.find_next_siblings():
                    sibling_links = sibling.find_all('a', href=True) if hasattr(sibling, 'find_all') else []
                    for link in sibling_links:
                        link_text = link.get_text(strip=True)
                        link_url = link.get('href')
                        if link_text and link_url:
                            full_url = urljoin(self.target_url, link_url)
                            links.append({
                                'unit_name': link_text,
                                'unit_link': full_url,
                                'has_link': True
                            })

                if links:
                    return {
                        'table_index': 0,  # 不在表格中
                        'table_title': '项目经理部',
                        'unit_count': len(links),
                        'units': links
                    }

        return None

    def save_results(self):
        """保存结果到CSV和JSON文件"""
        if not self.table_data:
            logger.warning("WARNING: 没有数据需要保存")
            print("WARNING: 没有数据需要保存")
            return

        # 准备CSV数据 - 扁平化结构
        csv_data = []
        for table in self.table_data:
            table_title = table['table_title']
            for unit in table['units']:
                csv_row = {
                    '表格名称': table_title,
                    '单位名称': unit['unit_name'],
                    '子链接': unit['unit_link'] if unit['has_link'] else '无链接',
                    '是否有链接': '是' if unit['has_link'] else '否',
                    '层次结构': f"{table_title}-{unit['unit_name']}"
                }
                csv_data.append(csv_row)

        # 保存CSV文件
        csv_file = 'newcraper/table_links_data.csv'
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            if csv_data:
                fieldnames = ['表格名称', '单位名称', '子链接', '是否有链接', '层次结构']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(csv_data)

        # 保存JSON文件 - 保持层次结构
        json_file = 'newcraper/table_links_data.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.table_data, f, ensure_ascii=False, indent=2)

        logger.info(f"SAVED: 数据已保存到 {csv_file} 和 {json_file}")
        print(f"\n数据已保存到:")
        print(f"  - table_links_data.csv (扁平化数据)")
        print(f"  - table_links_data.json (层次结构数据)")
        print(f"  - table_link_scraper_crawl4ai.log (详细日志)")

    def generate_summary_report(self):
        """生成汇总报告"""
        if not self.table_data:
            return

        print("\n" + "=" * 80)
        print("📊 表格和链接识别汇总报告")
        print("=" * 80)

        total_units = 0
        total_links = 0

        for table in self.table_data:
            table_title = table['table_title']
            unit_count = table['unit_count']
            link_count = sum(1 for unit in table['units'] if unit['has_link'])

            total_units += unit_count
            total_links += link_count

            print(f"\n📋 {table_title}")
            print(f"   单位数量: {unit_count}")
            print(f"   有链接的单位: {link_count}")
            print(f"   无链接的单位: {unit_count - link_count}")

            # 显示前几个单位作为示例
            if table['units']:
                print(f"   示例单位:")
                for i, unit in enumerate(table['units'][:3]):
                    link_status = "有链接" if unit['has_link'] else "无链接"
                    print(f"     - {unit['unit_name']} ({link_status})")
                if len(table['units']) > 3:
                    print(f"     ... 还有 {len(table['units']) - 3} 个单位")

        print(f"\n📈 总计统计:")
        print(f"   识别表格数量: {len(self.table_data)}")
        print(f"   识别单位总数: {total_units}")
        print(f"   有子链接单位: {total_links}")
        print(f"   无子链接单位: {total_units - total_links}")
        print(f"   链接覆盖率: {total_links/total_units*100:.1f}%" if total_units > 0 else "   链接覆盖率: 0%")

        # 与预期对比
        print(f"\n🎯 与预期结构对比:")
        for expected_title, expected_count in self.expected_tables.items():
            found = False
            for table in self.table_data:
                if expected_title in table['table_title'] or table['table_title'] in expected_title:
                    actual_count = table['unit_count']
                    status = "✅" if actual_count >= expected_count * 0.8 else "⚠️"
                    print(f"   {status} {expected_title}: 预期{expected_count}个，实际{actual_count}个")
                    found = True
                    break
            if not found:
                print(f"   ❌ {expected_title}: 预期{expected_count}个，未找到")

    async def run_scraper(self):
        """
        运行表格链接识别爬虫的主要方法

        执行流程：
        1. 测试crawl4ai连接
        2. 爬取网页内容
        3. 解析表格结构和链接
        4. 保存结果和生成报告
        """
        print("使用crawl4ai的表格链接识别爬虫")
        print("=" * 60)
        print("目标：识别5个表格+1个项目经理部的单位名称和子链接")
        print("=" * 60)

        # 第一步：测试crawl4ai服务连接
        if not await self.test_crawl4ai():
            print("ERROR: 无法连接到crawl4ai服务")
            return

        # 第二步：爬取网页内容
        html_content = await self.crawl_with_post_api()

        if not html_content:
            print("ERROR: 无法获取网页内容")
            return

        print(f"\n成功获取网页内容，长度: {len(html_content)} 字符")

        # 第三步：解析表格结构和链接
        self.table_data = self.extract_tables_and_links(html_content)

        # 第四步：显示结果和保存数据
        if self.table_data:
            # 生成汇总报告
            self.generate_summary_report()

            # 保存数据到文件
            self.save_results()

            print("\n🎉 表格链接识别完成！")
            print("💡 提示: 生成的数据可用于构建多层次电话所在单位结构")

        else:
            print("ERROR: 未识别到任何表格或链接数据")


async def main():
    """
    程序入口函数
    """
    # 创建爬虫实例
    scraper = TableLinkScraperCrawl4AI()

    # 运行爬虫
    await scraper.run_scraper()


if __name__ == "__main__":
    """
    程序启动点
    """
    asyncio.run(main())