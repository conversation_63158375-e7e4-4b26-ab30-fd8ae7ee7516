#!/usr/bin/env python3
"""
测试Firecrawl服务连接和功能
"""

import os
import asyncio
from typing import Optional

def test_firecrawl_import():
    """测试Firecrawl SDK导入"""
    try:
        from firecrawl import FirecrawlApp, AsyncFirecrawlApp, ScrapeOptions
        print("✅ SUCCESS: Firecrawl SDK导入成功")
        return True
    except ImportError as e:
        print(f"❌ ERROR: 无法导入Firecrawl SDK: {e}")
        print("请安装Firecrawl SDK: pip install firecrawl-py")
        return False

def test_api_key():
    """测试API密钥配置"""
    api_key = os.getenv('FIRECRAWL_API_KEY')
    if api_key:
        print(f"✅ SUCCESS: 找到API密钥 (长度: {len(api_key)})")
        return api_key
    else:
        print("❌ WARNING: 未找到FIRECRAWL_API_KEY环境变量")
        print("请设置环境变量或在代码中提供API密钥")
        return None

def test_sync_scraping(api_key: Optional[str] = None):
    """测试同步爬取功能"""
    if not api_key:
        print("❌ SKIP: 跳过同步爬取测试（无API密钥）")
        return False
    
    try:
        from firecrawl import FirecrawlApp
        
        # 初始化Firecrawl客户端
        app = FirecrawlApp(api_key=api_key)
        
        # 测试爬取一个简单页面
        test_url = "https://httpbin.org/html"
        print(f"🔍 INFO: 测试同步爬取: {test_url}")
        
        result = app.scrape_url(
            test_url,
            formats=['markdown', 'html']
        )
        
        if result and ('markdown' in result or 'html' in result):
            print("✅ SUCCESS: 同步爬取测试成功")
            content_length = len(result.get('markdown', '') or result.get('html', ''))
            print(f"📄 INFO: 获取内容长度: {content_length} 字符")
            return True
        else:
            print("❌ ERROR: 同步爬取测试失败，无返回内容")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: 同步爬取测试异常: {e}")
        return False

async def test_async_scraping(api_key: Optional[str] = None):
    """测试异步爬取功能"""
    if not api_key:
        print("❌ SKIP: 跳过异步爬取测试（无API密钥）")
        return False
    
    try:
        from firecrawl import AsyncFirecrawlApp
        
        # 初始化异步Firecrawl客户端
        app = AsyncFirecrawlApp(api_key=api_key)
        
        # 测试异步爬取一个简单页面
        test_url = "https://httpbin.org/html"
        print(f"🔍 INFO: 测试异步爬取: {test_url}")
        
        result = await app.scrape_url(
            test_url,
            formats=['markdown', 'html']
        )
        
        if result and ('markdown' in result or 'html' in result):
            print("✅ SUCCESS: 异步爬取测试成功")
            content_length = len(result.get('markdown', '') or result.get('html', ''))
            print(f"📄 INFO: 获取内容长度: {content_length} 字符")
            return True
        else:
            print("❌ ERROR: 异步爬取测试失败，无返回内容")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: 异步爬取测试异常: {e}")
        return False

def test_target_url_access(api_key: Optional[str] = None):
    """测试目标URL访问"""
    if not api_key:
        print("❌ SKIP: 跳过目标URL测试（无API密钥）")
        return False
    
    try:
        from firecrawl import FirecrawlApp
        
        # 初始化Firecrawl客户端
        app = FirecrawlApp(api_key=api_key)
        
        # 测试目标URL
        target_url = "http://***********/phone/20201227.html"
        print(f"🔍 INFO: 测试目标URL: {target_url}")
        
        result = app.scrape_url(
            target_url,
            formats=['html', 'markdown']
        )
        
        if result:
            html_content = result.get('html') or result.get('markdown', '')
            if html_content:
                print("✅ SUCCESS: 目标URL爬取成功")
                print(f"📄 INFO: 获取内容长度: {len(html_content)} 字符")
                
                # 检查是否包含电话相关内容
                if "电话" in html_content or "矿领导" in html_content:
                    print("✅ SUCCESS: 发现电话相关内容")
                else:
                    print("⚠️ WARNING: 未发现电话相关内容")
                
                return True
            else:
                print("❌ ERROR: 目标URL返回空内容")
                return False
        else:
            print("❌ ERROR: 目标URL爬取失败")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: 目标URL测试异常: {e}")
        return False

async def run_all_tests():
    """运行所有测试"""
    print("🧪 Firecrawl服务连接测试")
    print("=" * 50)
    
    # 测试1: SDK导入
    print("\n1️⃣ 测试SDK导入...")
    sdk_ok = test_firecrawl_import()
    
    if not sdk_ok:
        print("❌ 无法继续测试，请先安装Firecrawl SDK")
        return
    
    # 测试2: API密钥
    print("\n2️⃣ 测试API密钥配置...")
    api_key = test_api_key()
    
    # 测试3: 同步爬取
    print("\n3️⃣ 测试同步爬取功能...")
    sync_ok = test_sync_scraping(api_key)
    
    # 测试4: 异步爬取
    print("\n4️⃣ 测试异步爬取功能...")
    async_ok = await test_async_scraping(api_key)
    
    # 测试5: 目标URL
    print("\n5️⃣ 测试目标URL访问...")
    target_ok = test_target_url_access(api_key)
    
    # 测试结果汇总
    print("\n" + "=" * 50)
    print("🏁 测试结果汇总:")
    
    tests = [
        ("SDK导入", sdk_ok),
        ("API密钥", bool(api_key)),
        ("同步爬取", sync_ok),
        ("异步爬取", async_ok),
        ("目标URL", target_ok)
    ]
    
    passed = 0
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(tests)} 项测试通过")
    
    if passed >= 3:  # SDK导入 + API密钥 + 至少一个爬取测试
        print("🎉 SUCCESS: 基本功能测试通过，可以运行爬虫程序！")
    else:
        print("⚠️ WARNING: 部分测试失败，请检查配置")
    
    return passed >= 3

def main():
    """主函数"""
    asyncio.run(run_all_tests())

if __name__ == "__main__":
    main()
