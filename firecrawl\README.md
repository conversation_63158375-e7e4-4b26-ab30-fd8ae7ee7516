# 使用Firecrawl的矿领导电话表格爬虫

## 🔥 Firecrawl技术简介

Firecrawl是一个强大的网页爬取API服务，具有以下特点：
- **智能内容提取** - 自动处理JavaScript渲染的页面
- **多格式输出** - 支持HTML、Markdown、结构化数据等格式
- **反爬虫绕过** - 内置反爬虫检测绕过机制
- **高可靠性** - 企业级的稳定性和性能
- **简单易用** - 提供Python、Node.js等多种SDK

## 📁 项目结构

```
E:\mycode\pc1\firecrawl\
├── phone_scraper_firecrawl.py    # 主爬虫程序（需要API密钥）
├── phone_scraper_demo.py         # 演示版本（无需API密钥）
├── test_firecrawl.py             # Firecrawl连接测试
├── requirements.txt              # 依赖包列表
├── README.md                     # 使用说明（本文件）
├── phone_data_firecrawl.csv      # 输出：CSV格式数据
├── phone_data_firecrawl.json     # 输出：JSON格式数据
├── phone_scraper_firecrawl.log   # 输出：详细日志
└── phone_data_demo.csv           # 演示输出文件
```

## 🚀 快速开始

### 方式一：演示模式（推荐新手）

无需API密钥，使用模拟数据演示完整流程：

```bash
cd E:\mycode\pc1\firecrawl
pip install -r requirements.txt
python phone_scraper_demo.py
```

### 方式二：完整功能（需要API密钥）

1. **获取Firecrawl API密钥**
   - 访问 [Firecrawl官网](https://firecrawl.dev)
   - 注册账号并获取API密钥

2. **设置环境变量**
   ```bash
   # Windows
   set FIRECRAWL_API_KEY=your_api_key_here
   
   # Linux/Mac
   export FIRECRAWL_API_KEY=your_api_key_here
   ```

3. **安装依赖并运行**
   ```bash
   cd E:\mycode\pc1\firecrawl
   pip install -r requirements.txt
   python test_firecrawl.py          # 测试连接
   python phone_scraper_firecrawl.py # 运行爬虫
   ```

## 🔧 使用方法

### 基本用法

```bash
# 同步模式（默认）
python phone_scraper_firecrawl.py

# 异步模式
python phone_scraper_firecrawl.py --async

# 演示模式
python phone_scraper_demo.py

# 连接测试
python test_firecrawl.py
```

### 高级用法

```python
from phone_scraper_firecrawl import PhoneScraperFirecrawl

# 使用自定义API密钥
scraper = PhoneScraperFirecrawl(api_key="your_api_key")

# 运行同步爬虫
scraper.run_scraper_sync()

# 或运行异步爬虫
import asyncio
asyncio.run(scraper.run_scraper_async())
```

## 📊 输出格式

### CSV文件 (phone_data_firecrawl.csv)
```csv
电话号码,电话所在单位,用户名
7719001,矿领导,吴启明
7716398,矿领导,潘斌
7716109,矿领导,耿志强
...
```

### JSON文件 (phone_data_firecrawl.json)
```json
[
  {
    "phone": "7719001",
    "unit": "矿领导",
    "name": "吴启明",
    "extraction_method": "firecrawl"
  }
]
```

### 格式化输出
```text
7719001、矿领导、吴启明; 7716398、矿领导、潘斌; 7716109、矿领导、耿志强; ...
```

## 🔍 技术特点

### Firecrawl优势
- **JavaScript支持** - 能够处理动态加载的内容
- **反爬虫绕过** - 自动处理各种反爬虫机制
- **高成功率** - 企业级的爬取成功率
- **多格式支持** - HTML、Markdown、结构化数据
- **云端处理** - 无需本地浏览器环境

### 代码特点
- **详细注释** - 完整的中文注释说明
- **错误处理** - 完善的异常处理机制
- **双模式支持** - 同步和异步两种运行模式
- **演示功能** - 提供无API密钥的演示版本
- **测试工具** - 内置连接测试和功能验证

## 🧪 测试功能

运行测试脚本验证Firecrawl配置：

```bash
python test_firecrawl.py
```

测试项目包括：
- ✅ SDK导入测试
- ✅ API密钥配置检查
- ✅ 同步爬取功能测试
- ✅ 异步爬取功能测试
- ✅ 目标URL访问测试

## 🔧 故障排除

### 常见问题

1. **ImportError: No module named 'firecrawl'**
   ```bash
   pip install firecrawl-py
   ```

2. **API密钥错误**
   ```
   ERROR: 无法连接到Firecrawl服务
   ```
   - 检查API密钥是否正确设置
   - 确认API密钥是否有效
   - 检查网络连接

3. **目标URL无法访问**
   ```
   ERROR: 目标URL爬取失败
   ```
   - 确认内网URL是否可访问
   - 检查网络连接
   - 尝试使用演示模式

### 调试方法

1. **查看详细日志**
   ```bash
   type phone_scraper_firecrawl.log
   ```

2. **运行连接测试**
   ```bash
   python test_firecrawl.py
   ```

3. **使用演示模式**
   ```bash
   python phone_scraper_demo.py
   ```

## 🆚 技术对比

| 特性 | Firecrawl | crawl4ai | 直接HTTP |
|------|-----------|----------|----------|
| JavaScript支持 | ✅ 优秀 | ✅ 良好 | ❌ 不支持 |
| 反爬虫绕过 | ✅ 强 | ✅ 中等 | ❌ 弱 |
| 配置复杂度 | 🟢 简单 | 🟡 中等 | 🟢 简单 |
| 成本 | 🟡 付费API | 🟢 免费 | 🟢 免费 |
| 稳定性 | ✅ 高 | ✅ 中等 | 🟡 一般 |
| 性能 | ✅ 快 | 🟡 中等 | ✅ 快 |

## 💡 最佳实践

1. **API密钥管理**
   - 使用环境变量存储API密钥
   - 不要在代码中硬编码密钥
   - 定期轮换API密钥

2. **错误处理**
   - 始终检查API响应状态
   - 实现重试机制
   - 记录详细的错误日志

3. **性能优化**
   - 使用异步模式处理大量URL
   - 合理设置请求间隔
   - 缓存重复请求的结果

## 📈 扩展功能

### 批量爬取
```python
urls = [
    "http://***********/phone/page1.html",
    "http://***********/phone/page2.html"
]

for url in urls:
    scraper = PhoneScraperFirecrawl()
    scraper.target_url = url
    scraper.run_scraper_sync()
```

### 定时任务
```python
import schedule
import time

def job():
    scraper = PhoneScraperFirecrawl()
    scraper.run_scraper_sync()

schedule.every().day.at("09:00").do(job)

while True:
    schedule.run_pending()
    time.sleep(1)
```

## 🎯 总结

使用Firecrawl技术实现的矿领导电话表格爬虫具有以下优势：

- ✅ **企业级稳定性** - 使用成熟的商业爬取服务
- ✅ **强大的兼容性** - 支持各种复杂网页结构
- ✅ **简单易用** - 最少的配置，最大的功能
- ✅ **完整的功能** - 同步/异步、测试、演示一应俱全
- ✅ **详细的文档** - 完整的中文注释和使用说明

这个实现展示了如何使用现代化的网页爬取技术来解决实际的数据提取需求！
