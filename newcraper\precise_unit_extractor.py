#!/usr/bin/env python3
"""
精确提取网页中6个区域的单位名称和子链接
只提取真正的单位/部室信息，避免重复和无效数据
"""

import asyncio
import csv
import json
import re
from crawl4ai import AsyncWebCrawler
from bs4 import BeautifulSoup
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('precise_unit_extractor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PreciseUnitExtractor:
    def __init__(self):
        self.url = "http://172.18.1.16/phone/20201227.html"
        self.base_url = "http://172.18.1.16"
        self.units_data = []
        
        # 定义6个区域的预期单位
        self.expected_units = {
            "机关部室办公电话": [
                '行政工作部', '矿纪委', '风控内审部', '对外联络部', '企业管理部',
                '党委工作部', '工会工作部', '财务管理部', '供应销售部', '机动能源部',
                '党委组织部', '矿团委', '环境保护部', '科技质量部', '生产运营部',
                '党委宣传部', '人力资源部', '安全防尘部', '工程管理部', '计划发展部',
                '数字化部'
            ],
            "主要生产单位办公电话": [
                '采矿场', '泗洲选矿厂', '大山选矿厂', '精尾综合厂',
                '百泰公司', '尾矿回收厂', '化工公司', '新技术厂'
            ],
            "主要经营企业办公电话": [
                '江铜集团（德兴）铸造有限公司', '江铜集团（德兴）建设有限公司', 
                '江铜集团（德兴）实业有限公司'
            ],
            "辅助生产企业办公电话": [
                '运输部', '检化中心', '动力厂', '地测中心', '保卫部', '信息档案中心'
            ],
            "主要服务单位办公电话": [
                '后勤服务中心', '德铜宾馆'
            ],
            "项目经理部办公电话": [
                '（德兴）项目经理部办公电话'
            ]
        }

    async def extract_units(self):
        """提取单位信息"""
        logger.info(f"开始提取单位信息: {self.url}")
        
        async with AsyncWebCrawler(verbose=True) as crawler:
            try:
                # 爬取网页
                result = await crawler.arun(url=self.url)
                
                if not result.success:
                    logger.error(f"爬取失败: {result.error_message}")
                    return []
                
                logger.info("网页爬取成功，开始解析HTML")
                
                # 解析HTML
                soup = BeautifulSoup(result.html, 'html.parser')
                
                # 提取各个区域的单位
                self._extract_region_units(soup)
                
                logger.info(f"提取完成，共获得 {len(self.units_data)} 个单位")
                return self.units_data
                
            except Exception as e:
                logger.error(f"提取过程出错: {str(e)}")
                return []

    def _extract_region_units(self, soup):
        """提取各个区域的单位信息"""
        
        # 1. 提取机关部室办公电话 (表格1)
        self._extract_table_units(soup, "机关部室办公电话", 1)
        
        # 2. 提取主要生产单位办公电话 (表格2)
        self._extract_table_units(soup, "主要生产单位办公电话", 2)
        
        # 3. 提取主要经营企业办公电话 (表格3)
        self._extract_table_units(soup, "主要经营企业办公电话", 3)
        
        # 4. 提取辅助生产企业办公电话 (表格4)
        self._extract_table_units(soup, "辅助生产企业办公电话", 4)
        
        # 5. 提取主要服务单位办公电话 (表格5)
        self._extract_table_units(soup, "主要服务单位办公电话", 5)
        
        # 6. 提取项目经理部办公电话 (特殊处理)
        self._extract_project_manager_unit(soup)

    def _extract_table_units(self, soup, region_name, table_index):
        """提取表格中的单位信息"""
        logger.info(f"提取 {region_name} (表格{table_index})")
        
        # 查找所有表格
        tables = soup.find_all('table')
        
        if table_index <= len(tables):
            table = tables[table_index - 1]  # 表格索引从0开始
            
            # 查找表格中的所有链接
            links = table.find_all('a', href=True)
            
            expected_units = self.expected_units.get(region_name, [])
            found_units = []
            
            for link in links:
                unit_name = link.get_text(strip=True)
                unit_href = link.get('href')
                
                # 检查是否是预期的单位名称
                if unit_name in expected_units:
                    # 构建完整URL
                    if unit_href.startswith('/'):
                        full_url = self.base_url + unit_href
                    elif unit_href.startswith('http'):
                        full_url = unit_href
                    else:
                        full_url = f"{self.base_url}/phone/{unit_href}"
                    
                    unit_data = {
                        '单位名称': unit_name,
                        '子链接': full_url
                    }
                    
                    # 避免重复
                    if unit_data not in self.units_data:
                        self.units_data.append(unit_data)
                        found_units.append(unit_name)
                        logger.info(f"  找到单位: {unit_name} -> {full_url}")
            
            logger.info(f"  {region_name} 共找到 {len(found_units)} 个单位")
            
            # 检查是否有遗漏
            missing_units = set(expected_units) - set(found_units)
            if missing_units:
                logger.warning(f"  {region_name} 遗漏单位: {missing_units}")
        else:
            logger.warning(f"未找到表格{table_index}")

    def _extract_project_manager_unit(self, soup):
        """提取项目经理部单位信息 (特殊处理)"""
        logger.info("提取项目经理部办公电话")
        
        # 查找包含"项目经理部"的链接
        project_links = soup.find_all('a', href=True)
        
        for link in project_links:
            link_text = link.get_text(strip=True)
            if '项目经理部' in link_text:
                unit_href = link.get('href')
                
                # 构建完整URL
                if unit_href.startswith('/'):
                    full_url = self.base_url + unit_href
                elif unit_href.startswith('http'):
                    full_url = unit_href
                else:
                    full_url = f"{self.base_url}/phone/{unit_href}"
                
                unit_data = {
                    '单位名称': '（德兴）项目经理部办公电话',
                    '子链接': full_url
                }
                
                # 避免重复
                if unit_data not in self.units_data:
                    self.units_data.append(unit_data)
                    logger.info(f"  找到单位: （德兴）项目经理部办公电话 -> {full_url}")
                break

    def save_data(self):
        """保存数据"""
        if not self.units_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存CSV文件
        csv_file = 'newcraper/precise_units_data.csv'
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['单位名称', '子链接']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.units_data)
        
        # 保存JSON文件
        json_file = 'newcraper/precise_units_data.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.units_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据已保存到:")
        logger.info(f"  - {csv_file}")
        logger.info(f"  - {json_file}")
        
        # 生成统计报告
        self._generate_report()

    def _generate_report(self):
        """生成统计报告"""
        logger.info("\n" + "="*60)
        logger.info("📊 精确单位提取报告")
        logger.info("="*60)
        
        # 按区域统计
        region_stats = {}
        for region_name, expected_units in self.expected_units.items():
            found_units = [unit['单位名称'] for unit in self.units_data 
                          if unit['单位名称'] in expected_units]
            region_stats[region_name] = {
                'expected': len(expected_units),
                'found': len(found_units),
                'units': found_units
            }
        
        total_expected = sum(len(units) for units in self.expected_units.values())
        total_found = len(self.units_data)
        
        logger.info(f"\n📈 总体统计:")
        logger.info(f"   预期单位总数: {total_expected}")
        logger.info(f"   实际提取总数: {total_found}")
        logger.info(f"   提取准确率: {(total_found/total_expected)*100:.1f}%")
        
        logger.info(f"\n📋 分区域统计:")
        for region_name, stats in region_stats.items():
            status = "✅" if stats['found'] == stats['expected'] else "⚠️"
            logger.info(f"   {status} {region_name}: {stats['found']}/{stats['expected']}")
            if stats['found'] < stats['expected']:
                missing = set(self.expected_units[region_name]) - set(stats['units'])
                logger.info(f"      遗漏: {missing}")

async def main():
    """主函数"""
    extractor = PreciseUnitExtractor()
    
    # 提取单位信息
    units_data = await extractor.extract_units()
    
    if units_data:
        # 保存数据
        extractor.save_data()
        logger.info("✅ 精确单位提取任务完成！")
    else:
        logger.error("❌ 提取失败，没有获得任何数据")

if __name__ == "__main__":
    asyncio.run(main())
