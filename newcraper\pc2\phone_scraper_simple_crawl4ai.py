#!/usr/bin/env python3
"""
使用本地部署的crawl4ai爬取矿领导电话表格 (简化版本)
目标：http://172.18.1.16/phone/20201227.html 中的矿领导电话表格
crawl4ai地址：172.18.151.239:11235
"""

import asyncio
import re
import json
import csv
import logging
from typing import List, Dict, Optional
import aiohttp
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phone_scraper_simple_crawl4ai.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class SimplePhoneScraperCrawl4AI:
    """
    使用crawl4ai的矿领导电话表格爬虫类

    主要功能：
    1. 连接本地部署的crawl4ai服务
    2. 爬取指定网页的矿领导电话表格
    3. 解析HTML内容提取电话号码、单位和姓名
    4. 保存结果到CSV和JSON文件
    """

    def __init__(self):
        """初始化爬虫配置参数"""
        # 目标网页地址
        self.target_url = "http://172.18.1.16/phone/20201227.html"

        # 本地部署的crawl4ai服务地址
        self.crawl4ai_url = "http://172.18.151.239:11235"

        # 存储提取的电话数据
        self.phone_data = []

        # 电话号码正则表达式：匹配7位内线或11位手机号
        self.phone_pattern = r'\b\d{7,11}\b'

    async def test_crawl4ai(self) -> bool:
        """
        测试crawl4ai服务连接状态

        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            # 创建HTTP会话
            async with aiohttp.ClientSession() as session:
                # 发送健康检查请求到crawl4ai服务
                async with session.get(f"{self.crawl4ai_url}/health", timeout=10) as response:
                    if response.status == 200:
                        # 连接成功
                        logger.info("SUCCESS: crawl4ai服务连接成功")
                        print("SUCCESS: crawl4ai服务连接成功")
                        return True
                    else:
                        # 连接失败，记录状态码
                        logger.error(f"ERROR: crawl4ai连接失败: {response.status}")
                        print(f"ERROR: crawl4ai连接失败: {response.status}")
                        return False
        except Exception as e:
            # 连接异常，记录错误信息
            logger.error(f"ERROR: crawl4ai连接异常: {e}")
            print(f"ERROR: crawl4ai连接异常: {e}")
            return False

    async def crawl_with_simple_api(self) -> Optional[str]:
        """使用简化的crawl4ai API"""
        try:
            async with aiohttp.ClientSession() as session:
                # 尝试简单的GET请求到crawl端点
                crawl_url = f"{self.crawl4ai_url}/crawl"
                params = {
                    'url': self.target_url,
                    'format': 'html'
                }

                logger.info(f"INFO: 尝试简单API调用: {crawl_url}")
                print(f"INFO: 尝试简单API调用: {crawl_url}")

                async with session.get(crawl_url, params=params, timeout=60) as response:
                    if response.status == 200:
                        content = await response.text()
                        logger.info("SUCCESS: 简单API调用成功")
                        print("SUCCESS: 简单API调用成功")
                        return content
                    else:
                        error_text = await response.text()
                        logger.error(f"ERROR: 简单API调用失败: {response.status} - {error_text}")
                        print(f"ERROR: 简单API调用失败: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"ERROR: 简单API调用异常: {e}")
            print(f"ERROR: 简单API调用异常: {e}")
            return None

    async def crawl_with_post_api(self) -> Optional[str]:
        """
        使用crawl4ai的POST API进行网页爬取

        Returns:
            Optional[str]: 成功返回HTML内容，失败返回None
        """
        try:
            # 创建HTTP会话
            async with aiohttp.ClientSession() as session:
                # 构建crawl4ai API端点URL
                api_url = f"{self.crawl4ai_url}/crawl"

                # 构建请求载荷 - 包含要爬取的URL列表
                payload = {
                    'urls': [self.target_url]  # crawl4ai要求URLs为数组格式
                }

                # 设置请求头 - 包含内容类型和认证信息
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer 123456'  # crawl4ai服务的API Token
                }

                logger.info(f"INFO: 尝试POST API调用")
                print(f"INFO: 尝试POST API调用")

                # 发送POST请求到crawl4ai API
                async with session.post(api_url, json=payload, headers=headers, timeout=60) as response:
                    if response.status == 200:
                        # 解析API响应
                        result = await response.json()
                        logger.info(f"INFO: POST API响应键: {list(result.keys())}")
                        print(f"INFO: POST API响应键: {list(result.keys())}")

                        # 处理不同的响应格式
                        if 'task_id' in result:
                            # crawl4ai返回异步任务ID，需要轮询任务状态
                            logger.info(f"INFO: 获得任务ID: {result['task_id']}")
                            print(f"INFO: 获得任务ID: {result['task_id']}")
                            return await self.wait_for_task(session, result['task_id'])

                        elif result.get('results'):
                            # 同步返回结果，直接提取HTML内容
                            first_result = result['results'][0]
                            html_content = (first_result.get('cleaned_html') or
                                          first_result.get('raw_html') or
                                          first_result.get('html', ''))
                            if html_content:
                                logger.info("SUCCESS: POST API获取内容成功")
                                print("SUCCESS: POST API获取内容成功")
                                return html_content

                        # 无法从响应中获取有效内容
                        logger.error("ERROR: POST API无法获取内容")
                        print("ERROR: POST API无法获取内容")
                        return None
                    else:
                        error_text = await response.text()
                        logger.error(f"ERROR: POST API失败: {response.status} - {error_text}")
                        print(f"ERROR: POST API失败: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"ERROR: POST API异常: {e}")
            print(f"ERROR: POST API异常: {e}")
            return None

    async def wait_for_task(self, session: aiohttp.ClientSession, task_id: str) -> Optional[str]:
        """
        等待crawl4ai异步任务完成并获取结果

        Args:
            session: HTTP会话对象
            task_id: crawl4ai返回的任务ID

        Returns:
            Optional[str]: 成功返回HTML内容，失败返回None
        """
        try:
            # 轮询任务状态，最多尝试20次
            for attempt in range(20):
                # 等待3秒后再次检查任务状态
                await asyncio.sleep(3)

                # 构建任务状态查询URL
                status_url = f"{self.crawl4ai_url}/task/{task_id}"
                logger.info(f"INFO: 检查任务状态 (尝试 {attempt + 1}/20)")
                print(f"INFO: 检查任务状态 (尝试 {attempt + 1}/20)")

                # 设置认证头信息
                headers = {'Authorization': 'Bearer 123456'}

                # 发送任务状态查询请求
                async with session.get(status_url, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        result = await response.json()
                        status = result.get('status', 'unknown')

                        logger.info(f"INFO: 任务状态: {status}")
                        print(f"INFO: 任务状态: {status}")

                        if status == 'completed':
                            logger.info(f"DEBUG: 完成任务的结果键: {list(result.keys())}")
                            print(f"DEBUG: 完成任务的结果键: {list(result.keys())}")

                            # 尝试不同的结果路径
                            html_content = None

                            # 路径1: result.result.results[0]
                            if result.get('result') and result['result'].get('results'):
                                first_result = result['result']['results'][0]
                                html_content = (first_result.get('cleaned_html') or
                                              first_result.get('raw_html') or
                                              first_result.get('html', ''))

                            # 路径2: result.results[0]
                            elif result.get('results'):
                                first_result = result['results'][0]
                                html_content = (first_result.get('cleaned_html') or
                                              first_result.get('raw_html') or
                                              first_result.get('html', ''))

                            # 路径3: 直接在result中
                            elif result.get('cleaned_html') or result.get('raw_html') or result.get('html'):
                                html_content = (result.get('cleaned_html') or
                                              result.get('raw_html') or
                                              result.get('html', ''))

                            if html_content:
                                logger.info("SUCCESS: 异步任务完成，获取内容成功")
                                print("SUCCESS: 异步任务完成，获取内容成功")
                                return html_content
                            else:
                                logger.error(f"ERROR: 任务完成但无法找到HTML内容")
                                print(f"ERROR: 任务完成但无法找到HTML内容")
                                # 继续尝试，可能需要更多时间

                        elif status == 'failed':
                            logger.error(f"ERROR: 异步任务失败: {result.get('error', 'Unknown')}")
                            print(f"ERROR: 异步任务失败")
                            return None
                    else:
                        logger.warning(f"WARNING: 无法获取任务状态: {response.status}")
                        print(f"WARNING: 无法获取任务状态: {response.status}")

            logger.error("ERROR: 异步任务超时")
            print("ERROR: 异步任务超时")
            return None

        except Exception as e:
            logger.error(f"ERROR: 等待任务异常: {e}")
            print(f"ERROR: 等待任务异常: {e}")
            return None

    def extract_phone_data(self, html: str) -> List[Dict]:
        """
        从HTML内容中提取电话数据

        Args:
            html: 网页HTML内容

        Returns:
            List[Dict]: 提取的电话数据列表，每个字典包含phone、unit、name字段
        """
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html, 'html.parser')
        extracted = []

        logger.info("INFO: 开始解析HTML内容")
        print("INFO: 开始解析HTML内容")

        # 查找页面中的所有表格
        tables = soup.find_all('table')
        logger.info(f"INFO: 找到 {len(tables)} 个表格")
        print(f"INFO: 找到 {len(tables)} 个表格")

        # 遍历所有表格，查找包含矿领导电话信息的表格
        for table_idx, table in enumerate(tables):
            table_text = table.get_text()

            # 通过关键词识别目标表格
            if "矿领导" in table_text or "姓名" in table_text or "办公室电话" in table_text:
                logger.info(f"FOUND: 在第{table_idx+1}个表格中找到目标表格")
                print(f"FOUND: 在第{table_idx+1}个表格中找到目标表格")

                # 获取表格中的所有行
                rows = table.find_all('tr')

                # 遍历表格的每一行
                for row_idx, row in enumerate(rows):
                    # 获取行中的所有单元格
                    cells = row.find_all(['td', 'th'])

                    # 跳过空行或单元格数量过少的行
                    if len(cells) < 2:
                        continue

                    # 提取每个单元格的文本内容
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    row_text = ' '.join(cell_texts)

                    # 使用正则表达式查找电话号码
                    phones = re.findall(self.phone_pattern, row_text)

                    # 如果找到电话号码，进行进一步处理
                    if phones:
                        logger.info(f"PROCESSING: 第{row_idx+1}行找到电话: {phones}")
                        print(f"PROCESSING: 第{row_idx+1}行找到电话: {phones}")

                        # 处理每个找到的电话号码
                        for phone in phones:
                            # 查找与电话号码对应的姓名
                            name = ""
                            phone_cell_idx = -1

                            # 找到包含电话号码的单元格位置
                            for idx, cell_text in enumerate(cell_texts):
                                if phone in cell_text:
                                    phone_cell_idx = idx
                                    break

                            # 根据电话号码位置推断姓名位置
                            if phone_cell_idx > 0:
                                # 电话号码在后面，姓名通常在前一个单元格
                                name = cell_texts[phone_cell_idx - 1]
                            elif phone_cell_idx == 0 and len(cell_texts) > 1:
                                # 电话号码在第一列，姓名可能在第二列
                                name = cell_texts[1]

                            # 清理和验证姓名
                            name = self.clean_name(name)

                            # 验证电话号码和姓名的有效性
                            if self.is_valid_phone(phone) and name:
                                # 创建电话记录
                                record = {
                                    'phone': phone,
                                    'unit': '矿领导',  # 固定为矿领导
                                    'name': name
                                }
                                extracted.append(record)
                                logger.info(f"EXTRACTED: {phone}、矿领导、{name}")
                                print(f"EXTRACTED: {phone}、矿领导、{name}")

                break

        return extracted

    def clean_name(self, name: str) -> str:
        """
        清理和验证姓名字符串

        Args:
            name: 原始姓名字符串

        Returns:
            str: 清理后的姓名，无效时返回空字符串
        """
        if not name:
            return ""

        # 移除常见的职务和标识词
        name = re.sub(r'(姓名|联系人|负责人|主任|经理|厂长|部长|科长)', '', name)

        # 移除标点符号
        name = re.sub(r'[：:、，,\(\)（）\[\]【】]', '', name)

        # 移除数字
        name = re.sub(r'\d+', '', name)

        # 去除首尾空格
        name = name.strip()

        # 验证姓名格式：2-4个中文字符
        if 2 <= len(name) <= 4 and re.match(r'^[\u4e00-\u9fff]+$', name):
            return name
        return ""

    def is_valid_phone(self, phone: str) -> bool:
        """
        验证电话号码格式

        Args:
            phone: 电话号码字符串

        Returns:
            bool: 有效返回True，无效返回False
        """
        # 只接受7位内线电话或11位手机号码
        return len(phone) == 7 or len(phone) == 11

    async def run_scraper(self):
        """
        运行爬虫的主要方法

        执行流程：
        1. 测试crawl4ai连接
        2. 尝试不同的API调用方式获取网页内容
        3. 解析HTML提取电话数据
        4. 保存结果到文件
        """
        print("使用crawl4ai的矿领导电话表格爬虫 (简化版)")
        print("=" * 60)

        # 第一步：测试crawl4ai服务连接
        if not await self.test_crawl4ai():
            print("ERROR: 无法连接到crawl4ai服务")
            return

        # 第二步：尝试不同的API调用方式获取网页内容
        html_content = None

        # 方式1: 尝试简单GET请求（通常不支持）
        print("\n尝试方式1: 简单GET请求...")
        html_content = await self.crawl_with_simple_api()

        # 方式2: 使用POST请求（推荐方式）
        if not html_content:
            print("\n尝试方式2: POST请求...")
            html_content = await self.crawl_with_post_api()

        # 检查是否成功获取网页内容
        if not html_content:
            print("ERROR: 所有方式都无法获取网页内容")
            return

        # 第三步：从HTML内容中提取电话数据
        print(f"\n成功获取网页内容，长度: {len(html_content)} 字符")
        self.phone_data = self.extract_phone_data(html_content)

        # 第四步：处理和显示提取结果
        if self.phone_data:
            print(f"\n成功提取 {len(self.phone_data)} 条电话记录:")
            print("=" * 60)

            # 显示每条记录的详细信息
            for i, record in enumerate(self.phone_data, 1):
                print(f"{i:2d}. {record['phone']}、{record['unit']}、{record['name']}")

            # 第五步：保存数据到文件
            self.save_results()

            # 按要求的格式输出所有数据
            print("=" * 60)
            print("按要求格式输出:")
            formatted = []
            for record in self.phone_data:
                formatted.append(f"{record['phone']}、{record['unit']}、{record['name']}")
            print("; ".join(formatted))

        else:
            print("ERROR: 未提取到任何电话数据")

    def save_results(self):
        """保存结果到CSV和JSON文件"""
        # 保存CSV文件 - 使用中文列名，不包含extraction_method列
        with open('phone_data_crawl4ai.csv', 'w', newline='', encoding='utf-8') as f:
            # 定义中文列名
            fieldnames = ['电话号码', '电话所在单位', '用户名']
            writer = csv.DictWriter(f, fieldnames=fieldnames)

            # 写入表头
            writer.writeheader()

            # 写入数据行，映射字段名
            for record in self.phone_data:
                writer.writerow({
                    '电话号码': record['phone'],
                    '电话所在单位': record['unit'],
                    '用户名': record['name']
                })

        # 保存JSON文件 - 保持原有格式用于程序处理
        with open('phone_data_crawl4ai.json', 'w', encoding='utf-8') as f:
            json.dump(self.phone_data, f, ensure_ascii=False, indent=2)

        print(f"\n数据已保存到:")
        print(f"  - phone_data_crawl4ai.csv (中文列名)")
        print(f"  - phone_data_crawl4ai.json (完整数据)")
        print(f"  - phone_scraper_simple_crawl4ai.log (详细日志)")


async def main():
    """
    程序入口函数

    创建爬虫实例并运行爬取任务
    """
    # 创建爬虫实例
    scraper = SimplePhoneScraperCrawl4AI()

    # 运行爬虫
    await scraper.run_scraper()


if __name__ == "__main__":
    """
    程序启动点

    使用asyncio运行异步主函数
    """
    asyncio.run(main())
