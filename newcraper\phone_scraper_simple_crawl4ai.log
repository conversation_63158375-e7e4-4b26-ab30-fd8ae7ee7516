2025-05-25 12:17:58,502 - INFO - SUCCESS: crawl4ai服务连接成功
2025-05-25 12:17:58,503 - INFO - INFO: 尝试简单API调用: http://172.18.151.239:11235/crawl
2025-05-25 12:17:58,506 - ERROR - ERROR: 简单API调用失败: 405 - {"detail":"Method Not Allowed"}
2025-05-25 12:17:58,507 - INFO - INFO: 尝试POST API调用
2025-05-25 12:17:58,511 - ERROR - ERROR: POST API失败: 403 - {"detail":"Not authenticated"}
2025-05-25 12:18:44,766 - INFO - SUCCESS: crawl4ai服务连接成功
2025-05-25 12:18:44,767 - INFO - INFO: 尝试简单API调用: http://172.18.151.239:11235/crawl
2025-05-25 12:18:44,771 - ERROR - ERROR: 简单API调用失败: 405 - {"detail":"Method Not Allowed"}
2025-05-25 12:18:44,773 - INFO - INFO: 尝试POST API调用
2025-05-25 12:18:44,778 - INFO - INFO: POST API响应键: ['task_id']
2025-05-25 12:18:44,779 - INFO - INFO: 获得任务ID: df694b3a-625a-47b4-be63-fde6b80efc03
2025-05-25 12:18:47,779 - INFO - INFO: 检查任务状态 (尝试 1/20)
2025-05-25 12:18:47,783 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:18:50,784 - INFO - INFO: 检查任务状态 (尝试 2/20)
2025-05-25 12:18:50,787 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:18:53,788 - INFO - INFO: 检查任务状态 (尝试 3/20)
2025-05-25 12:18:53,791 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:18:56,792 - INFO - INFO: 检查任务状态 (尝试 4/20)
2025-05-25 12:18:56,795 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:18:59,796 - INFO - INFO: 检查任务状态 (尝试 5/20)
2025-05-25 12:18:59,800 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:02,801 - INFO - INFO: 检查任务状态 (尝试 6/20)
2025-05-25 12:19:02,804 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:05,806 - INFO - INFO: 检查任务状态 (尝试 7/20)
2025-05-25 12:19:05,808 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:08,809 - INFO - INFO: 检查任务状态 (尝试 8/20)
2025-05-25 12:19:08,812 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:11,813 - INFO - INFO: 检查任务状态 (尝试 9/20)
2025-05-25 12:19:11,816 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:14,817 - INFO - INFO: 检查任务状态 (尝试 10/20)
2025-05-25 12:19:14,820 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:17,822 - INFO - INFO: 检查任务状态 (尝试 11/20)
2025-05-25 12:19:17,825 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:20,825 - INFO - INFO: 检查任务状态 (尝试 12/20)
2025-05-25 12:19:20,828 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:23,829 - INFO - INFO: 检查任务状态 (尝试 13/20)
2025-05-25 12:19:23,832 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:26,832 - INFO - INFO: 检查任务状态 (尝试 14/20)
2025-05-25 12:19:26,835 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:29,836 - INFO - INFO: 检查任务状态 (尝试 15/20)
2025-05-25 12:19:29,839 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:32,826 - INFO - INFO: 检查任务状态 (尝试 16/20)
2025-05-25 12:19:32,829 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:35,831 - INFO - INFO: 检查任务状态 (尝试 17/20)
2025-05-25 12:19:35,834 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:38,834 - INFO - INFO: 检查任务状态 (尝试 18/20)
2025-05-25 12:19:38,837 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:41,837 - INFO - INFO: 检查任务状态 (尝试 19/20)
2025-05-25 12:19:41,840 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:44,841 - INFO - INFO: 检查任务状态 (尝试 20/20)
2025-05-25 12:19:44,844 - WARNING - WARNING: 无法获取任务状态: 403
2025-05-25 12:19:44,845 - ERROR - ERROR: 异步任务超时
2025-05-25 12:20:29,642 - INFO - SUCCESS: crawl4ai服务连接成功
2025-05-25 12:20:29,643 - INFO - INFO: 尝试简单API调用: http://172.18.151.239:11235/crawl
2025-05-25 12:20:29,647 - ERROR - ERROR: 简单API调用失败: 405 - {"detail":"Method Not Allowed"}
2025-05-25 12:20:29,647 - INFO - INFO: 尝试POST API调用
2025-05-25 12:20:29,651 - INFO - INFO: POST API响应键: ['task_id']
2025-05-25 12:20:29,652 - INFO - INFO: 获得任务ID: d2cadbe9-3011-46aa-9b64-d5ce9c675744
2025-05-25 12:20:32,653 - INFO - INFO: 检查任务状态 (尝试 1/20)
2025-05-25 12:20:32,659 - INFO - INFO: 任务状态: completed
2025-05-25 12:20:35,659 - INFO - INFO: 检查任务状态 (尝试 2/20)
2025-05-25 12:20:35,665 - INFO - INFO: 任务状态: completed
2025-05-25 12:20:38,666 - INFO - INFO: 检查任务状态 (尝试 3/20)
2025-05-25 12:20:38,672 - INFO - INFO: 任务状态: completed
2025-05-25 12:20:41,673 - INFO - INFO: 检查任务状态 (尝试 4/20)
2025-05-25 12:20:41,679 - INFO - INFO: 任务状态: completed
2025-05-25 12:20:44,680 - INFO - INFO: 检查任务状态 (尝试 5/20)
2025-05-25 12:20:44,686 - INFO - INFO: 任务状态: completed
2025-05-25 12:20:47,689 - INFO - INFO: 检查任务状态 (尝试 6/20)
2025-05-25 12:20:47,695 - INFO - INFO: 任务状态: completed
2025-05-25 12:20:50,697 - INFO - INFO: 检查任务状态 (尝试 7/20)
2025-05-25 12:20:50,704 - INFO - INFO: 任务状态: completed
2025-05-25 12:20:53,705 - INFO - INFO: 检查任务状态 (尝试 8/20)
2025-05-25 12:20:53,713 - INFO - INFO: 任务状态: completed
2025-05-25 12:20:56,714 - INFO - INFO: 检查任务状态 (尝试 9/20)
2025-05-25 12:20:56,720 - INFO - INFO: 任务状态: completed
2025-05-25 12:20:59,725 - INFO - INFO: 检查任务状态 (尝试 10/20)
2025-05-25 12:20:59,733 - INFO - INFO: 任务状态: completed
2025-05-25 12:21:02,742 - INFO - INFO: 检查任务状态 (尝试 11/20)
2025-05-25 12:21:02,748 - INFO - INFO: 任务状态: completed
2025-05-25 12:21:05,750 - INFO - INFO: 检查任务状态 (尝试 12/20)
2025-05-25 12:21:05,756 - INFO - INFO: 任务状态: completed
2025-05-25 12:21:08,758 - INFO - INFO: 检查任务状态 (尝试 13/20)
2025-05-25 12:21:08,765 - INFO - INFO: 任务状态: completed
2025-05-25 12:21:11,767 - INFO - INFO: 检查任务状态 (尝试 14/20)
2025-05-25 12:21:11,774 - INFO - INFO: 任务状态: completed
2025-05-25 12:21:14,775 - INFO - INFO: 检查任务状态 (尝试 15/20)
2025-05-25 12:21:14,782 - INFO - INFO: 任务状态: completed
2025-05-25 12:21:17,783 - INFO - INFO: 检查任务状态 (尝试 16/20)
2025-05-25 12:21:17,789 - INFO - INFO: 任务状态: completed
2025-05-25 12:21:20,790 - INFO - INFO: 检查任务状态 (尝试 17/20)
2025-05-25 12:21:20,796 - INFO - INFO: 任务状态: completed
2025-05-25 12:21:23,797 - INFO - INFO: 检查任务状态 (尝试 18/20)
2025-05-25 12:21:23,802 - INFO - INFO: 任务状态: completed
2025-05-25 12:21:26,804 - INFO - INFO: 检查任务状态 (尝试 19/20)
2025-05-25 12:21:26,810 - INFO - INFO: 任务状态: completed
2025-05-25 12:21:29,812 - INFO - INFO: 检查任务状态 (尝试 20/20)
2025-05-25 12:21:29,818 - INFO - INFO: 任务状态: completed
2025-05-25 12:21:29,818 - ERROR - ERROR: 异步任务超时
2025-05-25 12:22:25,387 - INFO - SUCCESS: crawl4ai服务连接成功
2025-05-25 12:22:25,388 - INFO - INFO: 尝试简单API调用: http://172.18.151.239:11235/crawl
2025-05-25 12:22:25,394 - ERROR - ERROR: 简单API调用失败: 405 - {"detail":"Method Not Allowed"}
2025-05-25 12:22:25,395 - INFO - INFO: 尝试POST API调用
2025-05-25 12:22:25,398 - INFO - INFO: POST API响应键: ['task_id']
2025-05-25 12:22:25,399 - INFO - INFO: 获得任务ID: 53392b76-8888-4f96-b2cc-cf26b530b97b
2025-05-25 12:22:28,404 - INFO - INFO: 检查任务状态 (尝试 1/20)
2025-05-25 12:22:28,412 - INFO - INFO: 任务状态: completed
2025-05-25 12:22:28,413 - INFO - DEBUG: 完成任务的结果键: ['status', 'created_at', 'results']
2025-05-25 12:22:28,413 - INFO - SUCCESS: 异步任务完成，获取内容成功
2025-05-25 12:22:28,425 - INFO - INFO: 开始解析HTML内容
2025-05-25 12:22:28,425 - INFO - INFO: 找到 14 个表格
2025-05-25 12:22:28,425 - INFO - FOUND: 在第5个表格中找到目标表格
2025-05-25 12:22:28,427 - INFO - PROCESSING: 第1行找到电话: ['7716119', '7716119', '7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018', '7777120', '7716119']
2025-05-25 12:22:28,430 - INFO - PROCESSING: 第3行找到电话: ['7716119', '7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018', '7777120', '7716119']
2025-05-25 12:22:28,431 - INFO - PROCESSING: 第5行找到电话: ['7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018']
2025-05-25 12:22:28,433 - INFO - PROCESSING: 第7行找到电话: ['7719001', '7716398', '7716109']
2025-05-25 12:22:28,435 - INFO - EXTRACTED: 7719001、矿领导、吴启明
2025-05-25 12:22:28,436 - INFO - EXTRACTED: 7716398、矿领导、潘斌
2025-05-25 12:22:28,436 - INFO - EXTRACTED: 7716109、矿领导、耿志强
2025-05-25 12:22:28,439 - INFO - PROCESSING: 第8行找到电话: ['7719006', '7719016', '7716399']
2025-05-25 12:22:28,440 - INFO - EXTRACTED: 7719006、矿领导、李建国
2025-05-25 12:22:28,441 - INFO - EXTRACTED: 7719016、矿领导、周云
2025-05-25 12:22:28,442 - INFO - EXTRACTED: 7716399、矿领导、周宏
2025-05-25 12:22:28,444 - INFO - PROCESSING: 第9行找到电话: ['7719998', '7716396', '7719025']
2025-05-25 12:22:28,444 - INFO - EXTRACTED: 7719998、矿领导、曾流生
2025-05-25 12:22:28,445 - INFO - EXTRACTED: 7716396、矿领导、王志强
2025-05-25 12:22:28,445 - INFO - EXTRACTED: 7719025、矿领导、张林水
2025-05-25 12:22:28,446 - INFO - PROCESSING: 第10行找到电话: ['7719020', '7719018']
2025-05-25 12:22:28,447 - INFO - EXTRACTED: 7719020、矿领导、曾芳
2025-05-25 12:22:28,447 - INFO - EXTRACTED: 7719018、矿领导、黄满武
2025-05-25 16:17:04,206 - INFO - SUCCESS: crawl4ai服务连接成功
2025-05-25 16:17:04,208 - INFO - INFO: 尝试简单API调用: http://172.18.151.239:11235/crawl
2025-05-25 16:17:04,212 - ERROR - ERROR: 简单API调用失败: 405 - {"detail":"Method Not Allowed"}
2025-05-25 16:17:04,213 - INFO - INFO: 尝试POST API调用
2025-05-25 16:17:04,219 - INFO - INFO: POST API响应键: ['task_id']
2025-05-25 16:17:04,219 - INFO - INFO: 获得任务ID: 515fe42d-b9d0-4c7c-be21-c49945260f5b
2025-05-25 16:17:07,221 - INFO - INFO: 检查任务状态 (尝试 1/20)
2025-05-25 16:17:07,228 - INFO - INFO: 任务状态: completed
2025-05-25 16:17:07,229 - INFO - DEBUG: 完成任务的结果键: ['status', 'created_at', 'results']
2025-05-25 16:17:07,229 - INFO - SUCCESS: 异步任务完成，获取内容成功
2025-05-25 16:17:07,240 - INFO - INFO: 开始解析HTML内容
2025-05-25 16:17:07,241 - INFO - INFO: 找到 14 个表格
2025-05-25 16:17:07,243 - INFO - FOUND: 在第5个表格中找到目标表格
2025-05-25 16:17:07,245 - INFO - PROCESSING: 第1行找到电话: ['7716119', '7716119', '7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018', '7777120', '7716119']
2025-05-25 16:17:07,248 - INFO - PROCESSING: 第3行找到电话: ['7716119', '7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018', '7777120', '7716119']
2025-05-25 16:17:07,250 - INFO - PROCESSING: 第5行找到电话: ['7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018']
2025-05-25 16:17:07,251 - INFO - PROCESSING: 第7行找到电话: ['7719001', '7716398', '7716109']
2025-05-25 16:17:07,253 - INFO - EXTRACTED: 7719001、矿领导、吴启明
2025-05-25 16:17:07,253 - INFO - EXTRACTED: 7716398、矿领导、潘斌
2025-05-25 16:17:07,253 - INFO - EXTRACTED: 7716109、矿领导、耿志强
2025-05-25 16:17:07,254 - INFO - PROCESSING: 第8行找到电话: ['7719006', '7719016', '7716399']
2025-05-25 16:17:07,255 - INFO - EXTRACTED: 7719006、矿领导、李建国
2025-05-25 16:17:07,255 - INFO - EXTRACTED: 7719016、矿领导、周云
2025-05-25 16:17:07,256 - INFO - EXTRACTED: 7716399、矿领导、周宏
2025-05-25 16:17:07,256 - INFO - PROCESSING: 第9行找到电话: ['7719998', '7716396', '7719025']
2025-05-25 16:17:07,256 - INFO - EXTRACTED: 7719998、矿领导、曾流生
2025-05-25 16:17:07,256 - INFO - EXTRACTED: 7716396、矿领导、王志强
2025-05-25 16:17:07,257 - INFO - EXTRACTED: 7719025、矿领导、张林水
2025-05-25 16:17:07,257 - INFO - PROCESSING: 第10行找到电话: ['7719020', '7719018']
2025-05-25 16:17:07,258 - INFO - EXTRACTED: 7719020、矿领导、曾芳
2025-05-25 16:17:07,258 - INFO - EXTRACTED: 7719018、矿领导、黄满武
2025-06-03 22:28:55,193 - INFO - SUCCESS: crawl4ai服务连接成功
2025-06-03 22:28:55,195 - INFO - INFO: 尝试简单API调用: http://172.18.151.239:11235/crawl
2025-06-03 22:28:55,200 - ERROR - ERROR: 简单API调用失败: 405 - {"detail":"Method Not Allowed"}
2025-06-03 22:28:55,200 - INFO - INFO: 尝试POST API调用
2025-06-03 22:28:55,204 - INFO - INFO: POST API响应键: ['task_id']
2025-06-03 22:28:55,205 - INFO - INFO: 获得任务ID: 52d1d546-1e78-4178-88ce-1bdfa0e7632e
2025-06-03 22:28:58,206 - INFO - INFO: 检查任务状态 (尝试 1/20)
2025-06-03 22:28:58,212 - INFO - INFO: 任务状态: completed
2025-06-03 22:28:58,212 - INFO - DEBUG: 完成任务的结果键: ['status', 'created_at', 'results']
2025-06-03 22:28:58,213 - INFO - SUCCESS: 异步任务完成，获取内容成功
2025-06-03 22:28:58,224 - INFO - INFO: 开始解析HTML内容
2025-06-03 22:28:58,225 - INFO - INFO: 找到 14 个表格
2025-06-03 22:28:58,227 - INFO - FOUND: 在第5个表格中找到目标表格
2025-06-03 22:28:58,230 - INFO - PROCESSING: 第1行找到电话: ['7716119', '7716119', '7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018', '7777120', '7716119']
2025-06-03 22:28:58,233 - INFO - PROCESSING: 第3行找到电话: ['7716119', '7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018', '7777120', '7716119']
2025-06-03 22:28:58,235 - INFO - PROCESSING: 第5行找到电话: ['7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018']
2025-06-03 22:28:58,236 - INFO - PROCESSING: 第7行找到电话: ['7719001', '7716398', '7716109']
2025-06-03 22:28:58,242 - INFO - EXTRACTED: 7719001、矿领导、吴启明
2025-06-03 22:28:58,242 - INFO - EXTRACTED: 7716398、矿领导、潘斌
2025-06-03 22:28:58,242 - INFO - EXTRACTED: 7716109、矿领导、耿志强
2025-06-03 22:28:58,243 - INFO - PROCESSING: 第8行找到电话: ['7719006', '7719016', '7716399']
2025-06-03 22:28:58,243 - INFO - EXTRACTED: 7719006、矿领导、李建国
2025-06-03 22:28:58,243 - INFO - EXTRACTED: 7719016、矿领导、周云
2025-06-03 22:28:58,244 - INFO - EXTRACTED: 7716399、矿领导、周宏
2025-06-03 22:28:58,244 - INFO - PROCESSING: 第9行找到电话: ['7719998', '7716396', '7719025']
2025-06-03 22:28:58,244 - INFO - EXTRACTED: 7719998、矿领导、曾流生
2025-06-03 22:28:58,245 - INFO - EXTRACTED: 7716396、矿领导、王志强
2025-06-03 22:28:58,246 - INFO - EXTRACTED: 7719025、矿领导、张林水
2025-06-03 22:28:58,246 - INFO - PROCESSING: 第10行找到电话: ['7719020', '7719018']
2025-06-03 22:28:58,246 - INFO - EXTRACTED: 7719020、矿领导、曾芳
2025-06-03 22:28:58,247 - INFO - EXTRACTED: 7719018、矿领导、黄满武
2025-06-03 22:34:56,903 - INFO - SUCCESS: crawl4ai服务连接成功
2025-06-03 22:34:56,904 - INFO - INFO: 尝试简单API调用: http://172.18.151.239:11235/crawl
2025-06-03 22:34:56,909 - ERROR - ERROR: 简单API调用失败: 405 - {"detail":"Method Not Allowed"}
2025-06-03 22:34:56,910 - INFO - INFO: 尝试POST API调用
2025-06-03 22:34:56,914 - INFO - INFO: POST API响应键: ['task_id']
2025-06-03 22:34:56,914 - INFO - INFO: 获得任务ID: 549bf6f5-f2f5-4939-85e5-94b87ec1d7a6
2025-06-03 22:34:59,915 - INFO - INFO: 检查任务状态 (尝试 1/20)
2025-06-03 22:34:59,922 - INFO - INFO: 任务状态: completed
2025-06-03 22:34:59,922 - INFO - DEBUG: 完成任务的结果键: ['status', 'created_at', 'results']
2025-06-03 22:34:59,923 - INFO - SUCCESS: 异步任务完成，获取内容成功
2025-06-03 22:34:59,936 - INFO - INFO: 开始解析HTML内容
2025-06-03 22:34:59,936 - INFO - INFO: 找到 14 个表格
2025-06-03 22:34:59,937 - INFO - FOUND: 在第5个表格中找到目标表格
2025-06-03 22:34:59,938 - INFO - PROCESSING: 第1行找到电话: ['7716119', '7716119', '7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018', '7777120', '7716119']
2025-06-03 22:34:59,940 - INFO - PROCESSING: 第3行找到电话: ['7716119', '7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018', '7777120', '7716119']
2025-06-03 22:34:59,941 - INFO - PROCESSING: 第5行找到电话: ['7719001', '7716398', '7716109', '7719006', '7719016', '7716399', '7719998', '7716396', '7719025', '7719020', '7719018']
2025-06-03 22:34:59,942 - INFO - PROCESSING: 第7行找到电话: ['7719001', '7716398', '7716109']
2025-06-03 22:34:59,947 - INFO - EXTRACTED: 7719001、矿领导、吴启明
2025-06-03 22:34:59,947 - INFO - EXTRACTED: 7716398、矿领导、潘斌
2025-06-03 22:34:59,948 - INFO - EXTRACTED: 7716109、矿领导、耿志强
2025-06-03 22:34:59,948 - INFO - PROCESSING: 第8行找到电话: ['7719006', '7719016', '7716399']
2025-06-03 22:34:59,948 - INFO - EXTRACTED: 7719006、矿领导、李建国
2025-06-03 22:34:59,948 - INFO - EXTRACTED: 7719016、矿领导、周云
2025-06-03 22:34:59,949 - INFO - EXTRACTED: 7716399、矿领导、周宏
2025-06-03 22:34:59,949 - INFO - PROCESSING: 第9行找到电话: ['7719998', '7716396', '7719025']
2025-06-03 22:34:59,949 - INFO - EXTRACTED: 7719998、矿领导、曾流生
2025-06-03 22:34:59,950 - INFO - EXTRACTED: 7716396、矿领导、王志强
2025-06-03 22:34:59,950 - INFO - EXTRACTED: 7719025、矿领导、张林水
2025-06-03 22:34:59,950 - INFO - PROCESSING: 第10行找到电话: ['7719020', '7719018']
2025-06-03 22:34:59,951 - INFO - EXTRACTED: 7719020、矿领导、曾芳
2025-06-03 22:34:59,951 - INFO - EXTRACTED: 7719018、矿领导、黄满武
