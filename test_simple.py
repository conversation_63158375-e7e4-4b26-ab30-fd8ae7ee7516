#!/usr/bin/env python3
"""
Simple connection test without Playwright - only tests crawl4ai service
"""

import asyncio
import aiohttp
import logging
from config import CRAWL4AI_URL, API_TOKEN, BASE_URL

# Configure logging - only to file
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

async def test_crawl4ai_health():
    """Test crawl4ai service health"""
    try:
        async with aiohttp.ClientSession() as session:
            health_url = f"{CRAWL4AI_URL}/health"
            logger.info(f"Testing health endpoint: {health_url}")
            print(f"Testing crawl4ai health: {health_url}")

            async with session.get(health_url, timeout=10) as response:
                if response.status == 200:
                    logger.info("SUCCESS: Crawl4AI service is healthy")
                    print("SUCCESS: Crawl4AI service is healthy")
                    return True
                else:
                    logger.error(f"ERROR: Health check failed: {response.status}")
                    print(f"ERROR: Health check failed: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"ERROR: Failed to connect to Crawl4AI: {e}")
        print(f"ERROR: Failed to connect to Crawl4AI: {e}")
        return False

async def test_crawl4ai_api():
    """Test crawl4ai API endpoint"""
    try:
        async with aiohttp.ClientSession() as session:
            api_url = f"{CRAWL4AI_URL}/crawl"
            headers = {
                'Authorization': f'Bearer {API_TOKEN}',
                'Content-Type': 'application/json'
            }

            # Simple test payload
            payload = {
                'urls': ['https://httpbin.org/html'],
                'extraction_strategy': 'NoExtractionStrategy'
            }

            logger.info(f"Testing API endpoint: {api_url}")
            print(f"Testing crawl4ai API: {api_url}")

            async with session.post(api_url, json=payload, headers=headers, timeout=30) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info("SUCCESS: Crawl4AI API is working")
                    print("SUCCESS: Crawl4AI API is working")
                    return True
                else:
                    logger.error(f"ERROR: API test failed: {response.status}")
                    print(f"ERROR: API test failed: {response.status}")
                    return False

    except Exception as e:
        logger.error(f"ERROR: API test exception: {e}")
        print(f"ERROR: API test exception: {e}")
        return False

async def test_target_accessibility():
    """Test if target URL is accessible"""
    try:
        async with aiohttp.ClientSession() as session:
            logger.info(f"Testing target URL accessibility: {BASE_URL}")
            print(f"Testing target URL: {BASE_URL}")

            async with session.get(BASE_URL, timeout=10) as response:
                if response.status == 200:
                    # Try different encodings for Chinese websites
                    try:
                        content = await response.text(encoding='utf-8')
                    except UnicodeDecodeError:
                        try:
                            content = await response.text(encoding='gbk')
                        except UnicodeDecodeError:
                            content = await response.text(encoding='gb2312')

                    logger.info("SUCCESS: Target URL is accessible")
                    print("SUCCESS: Target URL is accessible")

                    # Check for phone-related content
                    if "电话" in content or "phone" in content.lower() or "联系" in content:
                        logger.info("SUCCESS: Found phone-related content")
                        print("SUCCESS: Found phone-related content")
                    else:
                        logger.warning("WARNING: No phone-related content found")
                        print("WARNING: No phone-related content found")

                    return True
                else:
                    logger.error(f"ERROR: Target URL returned: {response.status}")
                    print(f"ERROR: Target URL returned: {response.status}")
                    return False

    except Exception as e:
        logger.error(f"ERROR: Target URL test failed: {e}")
        print(f"ERROR: Target URL test failed: {e}")
        return False

async def run_simple_tests():
    """Run simplified connection tests"""
    print("Simple Connection Tests")
    print("=" * 30)

    tests = [
        ("Crawl4AI Health", test_crawl4ai_health),
        ("Crawl4AI API", test_crawl4ai_api),
        ("Target URL", test_target_accessibility)
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        logger.info(f"RUNNING: {test_name} test...")

        try:
            result = await test_func()
            results[test_name] = result

            if result:
                print(f"PASS: {test_name}")
                logger.info(f"PASS: {test_name} test passed")
            else:
                print(f"FAIL: {test_name}")
                logger.error(f"FAIL: {test_name} test failed")

        except Exception as e:
            print(f"ERROR: {test_name} - {e}")
            logger.error(f"ERROR: {test_name} test error: {e}")
            results[test_name] = False

    # Summary
    print(f"\n" + "=" * 30)
    print("Test Results Summary:")
    logger.info("SUMMARY: Test Results Summary:")

    passed = sum(results.values())
    total = len(results)

    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
        logger.info(f"  {test_name}: {status}")

    print(f"\nOverall: {passed}/{total} tests passed")
    logger.info(f"Overall: {passed}/{total} tests passed")

    if passed == total:
        print("SUCCESS: All tests passed! Ready to run the scraper.")
        logger.info("SUCCESS: All tests passed! Ready to run the scraper.")
    else:
        print("ERROR: Some tests failed. Please check the configuration.")
        logger.error("ERROR: Some tests failed. Please check the configuration.")

    print(f"\nDetailed logs saved to: simple_test.log")
    return passed == total

if __name__ == "__main__":
    asyncio.run(run_simple_tests())
