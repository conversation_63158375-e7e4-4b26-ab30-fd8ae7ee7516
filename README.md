# Phone Information Web Scraper

A comprehensive web scraper using Crawl4AI to extract phone information from internal network webpages. This tool systematically extracts phone numbers, organizational units, and contact person names from complex hierarchical table structures.

## Features

- **Systematic Data Extraction**: Extracts phone numbers (7 or 11 digits), organizational units, and contact person names
- **Hierarchical Organization**: Builds proper organizational unit paths using '-' separator
- **Multi-page Scraping**: <PERSON><PERSON> main page and all sub-links automatically
- **Error Handling**: Robust error handling with retry mechanisms
- **Multiple Output Formats**: Saves data in both CSV and JSON formats
- **Connection Testing**: Built-in tests to verify Crawl4AI connectivity

## Prerequisites

- Python 3.12.9
- Crawl4AI Docker service running on **************:11235
- Access to internal network (***********)

## Installation

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Install Playwright browsers (if needed):**
   ```bash
   python install_playwright.py
   ```

3. **Start Crawl4AI Docker service:**
   ```bash
   docker run --rm -it -e CRAWL4AI_API_TOKEN=123456 -p 11235:11235 unclecode/crawl4ai:all-amd64
   ```

## Usage

### Quick Start

```bash
# Run with connection tests first (recommended)
python run_scraper.py --test

# Run scraper directly
python run_scraper.py

# Show help
python run_scraper.py --help
```

### Manual Testing

```bash
# Test Crawl4AI connection only
python test_connection.py
```

### Advanced Usage

```python
from phone_scraper import PhoneScraper
import asyncio

async def custom_scrape():
    scraper = PhoneScraper()
    data = await scraper.scrape_all_data()
    return data

# Run custom scraping
data = asyncio.run(custom_scrape())
```

## Configuration

Edit `config.py` to customize:

- **URLs**: Target webpage and Crawl4AI service endpoints
- **Patterns**: Phone number regex patterns
- **Keywords**: Exclusion and organizational keywords
- **Settings**: Request delays, timeouts, retry counts

## Data Structure

The scraper extracts data in the following format:

```
[Phone Number, Organizational Unit, Contact Person Name]
```

### Examples:
- `7719001, 矿领导, 吴启明`
- `7719218, 采矿场-场机关-场领导, 场长`
- `***********, 大山选矿厂-厂机关-供应室, 李建国`

## Scraping Strategy

### 1. Main Page Analysis
- Extracts direct phone table (11 phone numbers)
- Format: `7719001、矿领导、吴启明`

### 2. Sub-link Processing
Identifies and processes ~42 organizational unit sub-links:
- 21 administrative departments (机关部室)
- 8 main production units (主要生产单位)
- 3 main business units (主要经营单位)
- 6 main auxiliary production units (主要辅助生产单位)
- 2 main service units (主要服务单位)
- 1 project management department (项目经理部)
- 1 4G network section (4G专网手机号码) - excluded

### 3. Table Structure Handling
- **Simple tables**: Direct phone-name extraction
- **Complex tables**: Hierarchical organizational unit building
- **Column-by-column**: Systematic extraction maintaining structure

## Output Files

- **phone_data.csv**: Structured CSV with headers
- **phone_data.json**: JSON format for programmatic use
- **phone_scraper.log**: Detailed execution logs

## Error Handling

- **Connection timeouts**: Automatic retry with exponential backoff
- **Network issues**: Graceful degradation and logging
- **Parsing errors**: Skip problematic content, continue processing
- **Invalid data**: Validation and cleaning of extracted information

## Exclusions

The scraper automatically excludes:
- 4G专网手机号码 sections
- 第二人民医院急救电话
- 矿消防火警电话
- Non-person contact information

## Troubleshooting

### Common Issues

1. **Connection Failed**
   ```
   ❌ Failed to connect to Crawl4AI
   ```
   - Verify Docker service is running: `docker ps`
   - Check port 11235 is accessible
   - Verify API token matches

2. **No Data Extracted**
   ```
   ❌ No data was extracted
   ```
   - Check target URL accessibility
   - Verify webpage structure hasn't changed
   - Review logs for parsing errors

3. **Incomplete Results**
   ```
   ⚠️ Some sub-links failed
   ```
   - Network timeouts - increase delay in config
   - Server rate limiting - reduce concurrent requests
   - Check individual sub-link accessibility

### Debug Mode

Enable verbose logging by modifying the logging level in `phone_scraper.py`:

```python
logging.basicConfig(level=logging.DEBUG)
```

## Architecture

```
phone_scraper.py     # Main scraper class
├── PhoneScraper     # Core scraping logic
├── parse_main_page_table()    # Main page extraction
├── parse_sub_page_tables()    # Sub-page extraction
└── extract_phone_numbers()    # Phone pattern matching

test_connection.py   # Connection testing
config.py           # Configuration settings
run_scraper.py      # Simple execution wrapper
```

## Performance

- **Processing time**: ~5-10 minutes for full extraction
- **Memory usage**: <100MB typical
- **Network requests**: ~43 pages (1 main + 42 sub-links)
- **Rate limiting**: 1-second delay between requests

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Submit pull request

## License

Internal use only - Company proprietary software.
