#!/usr/bin/env python3
"""
调试HTML结构，分析网页的实际结构
"""

import asyncio
from crawl4ai import AsyncWebCrawler
from bs4 import BeautifulSoup
import re

async def debug_html_structure():
    """调试HTML结构"""
    url = "http://172.18.1.16/phone/20201227.html"
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        result = await crawler.arun(url=url)
        
        if not result.success:
            print(f"爬取失败: {result.error_message}")
            return
        
        soup = BeautifulSoup(result.html, 'html.parser')
        
        print("="*80)
        print("🔍 HTML结构分析")
        print("="*80)
        
        # 1. 查看所有表格
        tables = soup.find_all('table')
        print(f"\n📊 找到 {len(tables)} 个表格:")
        
        for i, table in enumerate(tables, 1):
            print(f"\n表格 {i}:")
            print(f"  - 行数: {len(table.find_all('tr'))}")
            print(f"  - 单元格数: {len(table.find_all(['td', 'th']))}")
            
            # 查看表格中的链接
            links = table.find_all('a', href=True)
            print(f"  - 链接数: {len(links)}")
            
            if links:
                print("  - 链接示例:")
                for j, link in enumerate(links[:5]):  # 只显示前5个
                    text = link.get_text(strip=True)
                    href = link.get('href')
                    print(f"    {j+1}. {text} -> {href}")
                if len(links) > 5:
                    print(f"    ... 还有 {len(links)-5} 个链接")
        
        # 2. 查看所有链接
        all_links = soup.find_all('a', href=True)
        print(f"\n🔗 页面总链接数: {len(all_links)}")
        
        # 3. 查找包含特定关键词的元素
        keywords = ['机关部室', '生产单位', '经营企业', '辅助生产', '服务单位', '项目经理']
        
        print(f"\n🔍 关键词搜索:")
        for keyword in keywords:
            elements = soup.find_all(text=re.compile(keyword))
            print(f"  - '{keyword}': 找到 {len(elements)} 个匹配")
            
            if elements:
                for elem in elements[:2]:  # 只显示前2个
                    parent = elem.parent if elem.parent else None
                    parent_name = parent.name if parent else "None"
                    print(f"    '{elem.strip()}' (父元素: {parent_name})")
        
        # 4. 查找特定单位名称
        unit_names = ['行政工作部', '采矿场', '运输部', '后勤服务中心']
        
        print(f"\n🏢 单位名称搜索:")
        for unit_name in unit_names:
            # 查找包含单位名称的链接
            unit_links = soup.find_all('a', text=re.compile(unit_name))
            print(f"  - '{unit_name}': 找到 {len(unit_links)} 个链接")
            
            if unit_links:
                for link in unit_links:
                    href = link.get('href')
                    print(f"    {unit_name} -> {href}")
            
            # 查找包含单位名称的文本
            unit_texts = soup.find_all(text=re.compile(unit_name))
            print(f"  - '{unit_name}': 找到 {len(unit_texts)} 个文本匹配")
        
        # 5. 保存HTML到文件用于进一步分析
        with open('newcraper/debug_html.html', 'w', encoding='utf-8') as f:
            f.write(result.html)
        
        print(f"\n💾 HTML已保存到: newcraper/debug_html.html")
        
        # 6. 查看页面的主要结构
        print(f"\n🏗️ 页面主要结构:")
        body = soup.find('body')
        if body:
            for child in body.children:
                if hasattr(child, 'name') and child.name:
                    print(f"  - {child.name}")
                    if child.name in ['div', 'table']:
                        # 查看div或table的class和id
                        class_attr = child.get('class', [])
                        id_attr = child.get('id', '')
                        if class_attr or id_attr:
                            print(f"    class: {class_attr}, id: {id_attr}")

if __name__ == "__main__":
    asyncio.run(debug_html_structure())
