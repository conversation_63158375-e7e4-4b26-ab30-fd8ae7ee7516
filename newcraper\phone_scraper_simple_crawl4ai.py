#!/usr/bin/env python3
"""
使用本地部署的crawl4ai爬取矿领导电话表格 (简化版本)
目标：http://172.18.1.16/phone/20201227.html 中的矿领导电话表格
crawl4ai地址：172.18.151.239:11235
"""

import asyncio
import re
import json
import csv
import logging
from typing import List, Dict, Optional
import aiohttp
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('newcraper/phone_scraper_simple_crawl4ai.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class SimplePhoneScraperCrawl4AI:
    def __init__(self):
        self.target_url = "http://172.18.1.16/phone/20201227.html"
        self.crawl4ai_url = "http://172.18.151.239:11235"
        self.phone_data = []
        self.phone_pattern = r'\b\d{7,11}\b'

    async def test_crawl4ai(self) -> bool:
        """测试crawl4ai连接"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.crawl4ai_url}/health", timeout=10) as response:
                    if response.status == 200:
                        logger.info("SUCCESS: crawl4ai服务连接成功")
                        print("SUCCESS: crawl4ai服务连接成功")
                        return True
                    else:
                        logger.error(f"ERROR: crawl4ai连接失败: {response.status}")
                        print(f"ERROR: crawl4ai连接失败: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"ERROR: crawl4ai连接异常: {e}")
            print(f"ERROR: crawl4ai连接异常: {e}")
            return False

    async def crawl_with_simple_api(self) -> Optional[str]:
        """使用简化的crawl4ai API"""
        try:
            async with aiohttp.ClientSession() as session:
                # 尝试简单的GET请求到crawl端点
                crawl_url = f"{self.crawl4ai_url}/crawl"
                params = {
                    'url': self.target_url,
                    'format': 'html'
                }

                logger.info(f"INFO: 尝试简单API调用: {crawl_url}")
                print(f"INFO: 尝试简单API调用: {crawl_url}")

                async with session.get(crawl_url, params=params, timeout=60) as response:
                    if response.status == 200:
                        content = await response.text()
                        logger.info("SUCCESS: 简单API调用成功")
                        print("SUCCESS: 简单API调用成功")
                        return content
                    else:
                        error_text = await response.text()
                        logger.error(f"ERROR: 简单API调用失败: {response.status} - {error_text}")
                        print(f"ERROR: 简单API调用失败: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"ERROR: 简单API调用异常: {e}")
            print(f"ERROR: 简单API调用异常: {e}")
            return None

    async def crawl_with_post_api(self) -> Optional[str]:
        """使用POST API"""
        try:
            async with aiohttp.ClientSession() as session:
                api_url = f"{self.crawl4ai_url}/crawl"

                # 最简单的POST请求
                payload = {
                    'urls': [self.target_url]
                }

                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer 123456'
                }

                logger.info(f"INFO: 尝试POST API调用")
                print(f"INFO: 尝试POST API调用")

                async with session.post(api_url, json=payload, headers=headers, timeout=60) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"INFO: POST API响应键: {list(result.keys())}")
                        print(f"INFO: POST API响应键: {list(result.keys())}")

                        # 处理不同的响应格式
                        if 'task_id' in result:
                            logger.info(f"INFO: 获得任务ID: {result['task_id']}")
                            print(f"INFO: 获得任务ID: {result['task_id']}")
                            return await self.wait_for_task(session, result['task_id'])

                        elif result.get('results'):
                            first_result = result['results'][0]
                            html_content = (first_result.get('cleaned_html') or
                                          first_result.get('raw_html') or
                                          first_result.get('html', ''))
                            if html_content:
                                logger.info("SUCCESS: POST API获取内容成功")
                                print("SUCCESS: POST API获取内容成功")
                                return html_content

                        logger.error("ERROR: POST API无法获取内容")
                        print("ERROR: POST API无法获取内容")
                        return None
                    else:
                        error_text = await response.text()
                        logger.error(f"ERROR: POST API失败: {response.status} - {error_text}")
                        print(f"ERROR: POST API失败: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"ERROR: POST API异常: {e}")
            print(f"ERROR: POST API异常: {e}")
            return None

    async def wait_for_task(self, session: aiohttp.ClientSession, task_id: str) -> Optional[str]:
        """等待异步任务完成"""
        try:
            for attempt in range(20):  # 最多等待20次
                await asyncio.sleep(3)

                status_url = f"{self.crawl4ai_url}/task/{task_id}"
                logger.info(f"INFO: 检查任务状态 (尝试 {attempt + 1}/20)")
                print(f"INFO: 检查任务状态 (尝试 {attempt + 1}/20)")

                headers = {'Authorization': 'Bearer 123456'}
                async with session.get(status_url, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        result = await response.json()
                        status = result.get('status', 'unknown')

                        logger.info(f"INFO: 任务状态: {status}")
                        print(f"INFO: 任务状态: {status}")

                        if status == 'completed':
                            logger.info(f"DEBUG: 完成任务的结果键: {list(result.keys())}")
                            print(f"DEBUG: 完成任务的结果键: {list(result.keys())}")

                            # 尝试不同的结果路径
                            html_content = None

                            # 路径1: result.result.results[0]
                            if result.get('result') and result['result'].get('results'):
                                first_result = result['result']['results'][0]
                                html_content = (first_result.get('cleaned_html') or
                                              first_result.get('raw_html') or
                                              first_result.get('html', ''))

                            # 路径2: result.results[0]
                            elif result.get('results'):
                                first_result = result['results'][0]
                                html_content = (first_result.get('cleaned_html') or
                                              first_result.get('raw_html') or
                                              first_result.get('html', ''))

                            # 路径3: 直接在result中
                            elif result.get('cleaned_html') or result.get('raw_html') or result.get('html'):
                                html_content = (result.get('cleaned_html') or
                                              result.get('raw_html') or
                                              result.get('html', ''))

                            if html_content:
                                logger.info("SUCCESS: 异步任务完成，获取内容成功")
                                print("SUCCESS: 异步任务完成，获取内容成功")
                                return html_content
                            else:
                                logger.error(f"ERROR: 任务完成但无法找到HTML内容")
                                print(f"ERROR: 任务完成但无法找到HTML内容")
                                # 继续尝试，可能需要更多时间

                        elif status == 'failed':
                            logger.error(f"ERROR: 异步任务失败: {result.get('error', 'Unknown')}")
                            print(f"ERROR: 异步任务失败")
                            return None
                    else:
                        logger.warning(f"WARNING: 无法获取任务状态: {response.status}")
                        print(f"WARNING: 无法获取任务状态: {response.status}")

            logger.error("ERROR: 异步任务超时")
            print("ERROR: 异步任务超时")
            return None

        except Exception as e:
            logger.error(f"ERROR: 等待任务异常: {e}")
            print(f"ERROR: 等待任务异常: {e}")
            return None

    def extract_phone_data(self, html: str) -> List[Dict]:
        """提取电话数据"""
        soup = BeautifulSoup(html, 'html.parser')
        extracted = []

        logger.info("INFO: 开始解析HTML内容")
        print("INFO: 开始解析HTML内容")

        # 查找表格
        tables = soup.find_all('table')
        logger.info(f"INFO: 找到 {len(tables)} 个表格")
        print(f"INFO: 找到 {len(tables)} 个表格")

        for table_idx, table in enumerate(tables):
            table_text = table.get_text()
            if "矿领导" in table_text or "姓名" in table_text or "办公室电话" in table_text:
                logger.info(f"FOUND: 在第{table_idx+1}个表格中找到目标表格")
                print(f"FOUND: 在第{table_idx+1}个表格中找到目标表格")

                rows = table.find_all('tr')
                for row_idx, row in enumerate(rows):
                    cells = row.find_all(['td', 'th'])
                    if len(cells) < 2:
                        continue

                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    row_text = ' '.join(cell_texts)

                    phones = re.findall(self.phone_pattern, row_text)
                    if phones:
                        logger.info(f"PROCESSING: 第{row_idx+1}行找到电话: {phones}")
                        print(f"PROCESSING: 第{row_idx+1}行找到电话: {phones}")

                        for phone in phones:
                            # 查找姓名
                            name = ""
                            phone_cell_idx = -1
                            for idx, cell_text in enumerate(cell_texts):
                                if phone in cell_text:
                                    phone_cell_idx = idx
                                    break

                            if phone_cell_idx > 0:
                                name = cell_texts[phone_cell_idx - 1]
                            elif phone_cell_idx == 0 and len(cell_texts) > 1:
                                name = cell_texts[1]

                            # 清理姓名
                            name = self.clean_name(name)

                            if self.is_valid_phone(phone) and name:
                                record = {
                                    'phone': phone,
                                    'unit': '矿领导',
                                    'name': name,
                                    'extraction_method': 'crawl4ai'
                                }
                                extracted.append(record)
                                logger.info(f"EXTRACTED: {phone}、矿领导、{name}")
                                print(f"EXTRACTED: {phone}、矿领导、{name}")

                break

        return extracted

    def clean_name(self, name: str) -> str:
        """清理姓名"""
        if not name:
            return ""

        name = re.sub(r'(姓名|联系人|负责人|主任|经理|厂长|部长|科长)', '', name)
        name = re.sub(r'[：:、，,\(\)（）\[\]【】]', '', name)
        name = re.sub(r'\d+', '', name)
        name = name.strip()

        if 2 <= len(name) <= 4 and re.match(r'^[\u4e00-\u9fff]+$', name):
            return name
        return ""

    def is_valid_phone(self, phone: str) -> bool:
        """验证电话号码"""
        return len(phone) == 7 or len(phone) == 11

    async def run_scraper(self):
        """运行爬虫"""
        print("使用crawl4ai的矿领导电话表格爬虫 (简化版)")
        print("=" * 60)

        # 测试连接
        if not await self.test_crawl4ai():
            print("ERROR: 无法连接到crawl4ai服务")
            return

        # 尝试不同的API调用方式
        html_content = None

        # 方式1: 简单GET请求
        print("\n尝试方式1: 简单GET请求...")
        html_content = await self.crawl_with_simple_api()

        # 方式2: POST请求
        if not html_content:
            print("\n尝试方式2: POST请求...")
            html_content = await self.crawl_with_post_api()

        if not html_content:
            print("ERROR: 所有方式都无法获取网页内容")
            return

        # 提取数据
        print(f"\n成功获取网页内容，长度: {len(html_content)} 字符")
        self.phone_data = self.extract_phone_data(html_content)

        if self.phone_data:
            print(f"\n成功提取 {len(self.phone_data)} 条电话记录:")
            print("=" * 60)

            for i, record in enumerate(self.phone_data, 1):
                print(f"{i:2d}. {record['phone']}、{record['unit']}、{record['name']}")

            # 保存数据
            self.save_results()

            print("=" * 60)
            print("按要求格式输出:")
            formatted = []
            for record in self.phone_data:
                formatted.append(f"{record['phone']}、{record['unit']}、{record['name']}")
            print("; ".join(formatted))

        else:
            print("ERROR: 未提取到任何电话数据")

    def save_results(self):
        """保存结果"""
        # 保存CSV
        with open('newcraper/phone_data_crawl4ai.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['phone', 'unit', 'name', 'extraction_method'])
            writer.writeheader()
            for record in self.phone_data:
                writer.writerow(record)

        # 保存JSON
        with open('newcraper/phone_data_crawl4ai.json', 'w', encoding='utf-8') as f:
            json.dump(self.phone_data, f, ensure_ascii=False, indent=2)

        print(f"\n数据已保存到:")
        print(f"  - newcraper/phone_data_crawl4ai.csv")
        print(f"  - newcraper/phone_data_crawl4ai.json")
        print(f"  - newcraper/phone_scraper_simple_crawl4ai.log")


async def main():
    scraper = SimplePhoneScraperCrawl4AI()
    await scraper.run_scraper()


if __name__ == "__main__":
    asyncio.run(main())
