# 🔥 Firecrawl爬虫快速开始指南

## 🎯 项目简介

使用Firecrawl技术重新实现矿领导电话表格爬虫，提供企业级的网页爬取能力。

## 🚀 立即体验（演示模式）

**无需任何配置，直接运行：**

```bash
cd E:\mycode\pc1\firecrawl
python phone_scraper_demo.py
```

**演示结果：**
```
🎉 成功提取 11 条电话记录:
 1. 7719001、矿领导、吴启明
 2. 7716398、矿领导、潘斌
 3. 7716109、矿领导、耿志强
 4. 7719006、矿领导、李建国
 5. 7719016、矿领导、周云
 6. 7716399、矿领导、周宏
 7. 7719998、矿领导、曾流生
 8. 7716396、矿领导、王志强
 9. 7719025、矿领导、张林水
10. 7719020、矿领导、曾芳
11. 7719018、矿领导、黄满武

按要求格式输出:
7719001、矿领导、吴启明; 7716398、矿领导、潘斌; 7716109、矿领导、耿志强; ...
```

## 📁 文件说明

| 文件名 | 说明 | 是否需要API密钥 |
|--------|------|----------------|
| `phone_scraper_demo.py` | 演示版本 | ❌ 不需要 |
| `phone_scraper_firecrawl.py` | 完整版本 | ✅ 需要 |
| `test_firecrawl.py` | 连接测试 | ✅ 需要 |
| `requirements.txt` | 依赖包 | - |

## 🔑 使用真实Firecrawl服务

### 1. 获取API密钥
- 访问 [Firecrawl官网](https://firecrawl.dev)
- 注册账号并获取免费API密钥

### 2. 设置环境变量
```bash
# Windows命令行
set FIRECRAWL_API_KEY=your_api_key_here

# PowerShell
$env:FIRECRAWL_API_KEY="your_api_key_here"
```

### 3. 安装依赖
```bash
pip install firecrawl-py beautifulsoup4 lxml
```

### 4. 测试连接
```bash
python test_firecrawl.py
```

### 5. 运行爬虫
```bash
# 同步模式
python phone_scraper_firecrawl.py

# 异步模式
python phone_scraper_firecrawl.py --async
```

## 📊 输出文件

运行后会生成以下文件：

- **`phone_data_firecrawl.csv`** - 中文列名的CSV文件
- **`phone_data_firecrawl.json`** - JSON格式的完整数据
- **`phone_scraper_firecrawl.log`** - 详细的运行日志

## 🆚 技术对比

| 特性 | Firecrawl | crawl4ai | 直接HTTP |
|------|-----------|----------|----------|
| **易用性** | 🟢 极简 | 🟡 中等 | 🟢 简单 |
| **功能强度** | 🟢 强大 | 🟡 良好 | 🔴 基础 |
| **成本** | 🟡 付费 | 🟢 免费 | 🟢 免费 |
| **稳定性** | 🟢 企业级 | 🟡 良好 | 🔴 一般 |

## 💡 选择建议

- **学习和演示** → 使用 `phone_scraper_demo.py`
- **生产环境** → 使用 `phone_scraper_firecrawl.py` + API密钥
- **本地部署** → 使用 crawl4ai 版本
- **简单需求** → 使用直接HTTP版本

## 🎯 核心优势

✅ **企业级稳定性** - 商业级爬取服务  
✅ **零配置演示** - 无需API密钥即可体验  
✅ **完整功能** - 同步/异步双模式支持  
✅ **详细文档** - 完整的中文注释和说明  
✅ **多格式输出** - CSV、JSON、格式化文本  

## 🔧 故障排除

**问题1：ImportError**
```bash
pip install firecrawl-py
```

**问题2：API密钥错误**
- 检查环境变量设置
- 确认API密钥有效性

**问题3：网络问题**
- 先运行演示模式验证代码逻辑
- 检查网络连接

## 📞 技术支持

遇到问题？
1. 查看 `README.md` 详细文档
2. 运行 `test_firecrawl.py` 诊断问题
3. 检查日志文件获取详细错误信息

---

**🎉 现在就开始体验Firecrawl的强大功能吧！**
