#!/usr/bin/env python3
"""
清理和去重表格链接数据
从爬取的原始数据中提取有用的单位名称和链接信息
"""

import csv
import json
from collections import defaultdict

def clean_and_deduplicate():
    """清理和去重数据"""
    
    # 读取原始CSV数据
    raw_data = []
    with open('newcraper/table_links_data.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            raw_data.append(row)
    
    print(f"原始数据总数: {len(raw_data)} 条")
    
    # 去重和清理
    unique_units = {}  # 用于去重
    clean_data = []
    
    # 定义有效的单位名称
    valid_units = {
        # 机关部室 (21个)
        '行政工作部', '矿纪委', '风控内审部', '对外联络部', '企业管理部',
        '党委工作部', '工会工作部', '财务管理部', '供应销售部', '机动能源部',
        '党委组织部', '矿团委', '环境保护部', '科技质量部', '生产运营部',
        '党委宣传部', '人力资源部', '安全防尘部', '工程管理部', '计划发展部',
        '数字化部',
        
        # 主要生产单位 (8个)
        '采矿场', '泗洲选矿厂', '大山选矿厂', '精尾综合厂',
        '百泰公司', '尾矿回收厂', '化工公司', '新技术厂',
        
        # 主要经营企业 (3个)
        '江铜集团（德兴）铸造有限公司', '江铜集团（德兴）建设有限公司', '江铜集团（德兴）实业有限公司',
        
        # 辅助生产企业 (6个)
        '运输部', '检化中心', '动力厂', '地测中心', '保卫部', '信息档案中心',
        
        # 主要服务单位 (2个)
        '后勤服务中心', '德铜宾馆',
        
        # 项目经理部 (1个)
        '（德兴）项目经理部办公电话',
        
        # 其他
        '4G专网手机号码'
    }
    
    # 处理数据
    for row in raw_data:
        unit_name = row['单位名称']
        unit_link = row['子链接']
        has_link = row['是否有链接'] == '是'
        
        # 只保留有效的单位名称
        if unit_name in valid_units and has_link and unit_link != '无链接':
            # 去重
            if unit_name not in unique_units:
                # 确定单位类别
                category = get_unit_category(unit_name)
                
                clean_record = {
                    '单位名称': unit_name,
                    '子链接': unit_link,
                    '单位类别': category,
                    '层次结构': f"{category}-{unit_name}"
                }
                
                unique_units[unit_name] = clean_record
                clean_data.append(clean_record)
    
    print(f"清理后数据总数: {len(clean_data)} 条")
    
    # 按类别统计
    category_stats = defaultdict(int)
    for record in clean_data:
        category_stats[record['单位类别']] += 1
    
    print("\n按类别统计:")
    for category, count in category_stats.items():
        print(f"  {category}: {count} 个单位")
    
    # 保存清理后的数据
    save_clean_data(clean_data)
    
    return clean_data

def get_unit_category(unit_name):
    """根据单位名称确定类别"""
    
    # 机关部室
    if unit_name in ['行政工作部', '矿纪委', '风控内审部', '对外联络部', '企业管理部',
                     '党委工作部', '工会工作部', '财务管理部', '供应销售部', '机动能源部',
                     '党委组织部', '矿团委', '环境保护部', '科技质量部', '生产运营部',
                     '党委宣传部', '人力资源部', '安全防尘部', '工程管理部', '计划发展部',
                     '数字化部']:
        return "机关部室办公电话"
    
    # 主要生产单位
    elif unit_name in ['采矿场', '泗洲选矿厂', '大山选矿厂', '精尾综合厂',
                       '百泰公司', '尾矿回收厂', '化工公司', '新技术厂']:
        return "主要生产单位办公电话"
    
    # 主要经营企业
    elif unit_name in ['江铜集团（德兴）铸造有限公司', '江铜集团（德兴）建设有限公司', '江铜集团（德兴）实业有限公司']:
        return "主要经营企业办公电话"
    
    # 辅助生产企业
    elif unit_name in ['运输部', '检化中心', '动力厂', '地测中心', '保卫部', '信息档案中心']:
        return "辅助生产企业办公电话"
    
    # 主要服务单位
    elif unit_name in ['后勤服务中心', '德铜宾馆']:
        return "主要服务单位办公电话"
    
    # 项目经理部
    elif unit_name == '（德兴）项目经理部办公电话':
        return "项目经理部办公电话"
    
    # 其他
    elif unit_name == '4G专网手机号码':
        return "其他"
    
    else:
        return "未分类"

def save_clean_data(clean_data):
    """保存清理后的数据"""
    
    # 保存CSV文件
    csv_file = 'newcraper/clean_table_links.csv'
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        fieldnames = ['单位名称', '子链接', '单位类别', '层次结构']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(clean_data)
    
    # 保存JSON文件
    json_file = 'newcraper/clean_table_links.json'
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(clean_data, f, ensure_ascii=False, indent=2)
    
    # 按类别组织数据
    organized_data = defaultdict(list)
    for record in clean_data:
        organized_data[record['单位类别']].append({
            '单位名称': record['单位名称'],
            '子链接': record['子链接']
        })
    
    # 保存按类别组织的JSON文件
    organized_file = 'newcraper/organized_table_links.json'
    with open(organized_file, 'w', encoding='utf-8') as f:
        json.dump(dict(organized_data), f, ensure_ascii=False, indent=2)
    
    print(f"\n清理后的数据已保存到:")
    print(f"  - {csv_file}")
    print(f"  - {json_file}")
    print(f"  - {organized_file}")

def generate_summary():
    """生成汇总报告"""
    
    # 读取清理后的数据
    with open('newcraper/clean_table_links.json', 'r', encoding='utf-8') as f:
        clean_data = json.load(f)
    
    print("\n" + "=" * 80)
    print("📊 表格链接识别最终汇总报告")
    print("=" * 80)
    
    # 按类别统计
    category_stats = defaultdict(list)
    for record in clean_data:
        category_stats[record['单位类别']].append(record['单位名称'])
    
    total_units = len(clean_data)
    total_categories = len(category_stats)
    
    print(f"\n📈 总体统计:")
    print(f"   识别表格类别: {total_categories} 个")
    print(f"   识别单位总数: {total_units} 个")
    print(f"   有效链接覆盖率: 100%")
    
    print(f"\n📋 详细分类统计:")
    for category, units in category_stats.items():
        print(f"\n🔸 {category}")
        print(f"   单位数量: {len(units)} 个")
        print(f"   单位列表: {', '.join(units[:5])}")
        if len(units) > 5:
            print(f"   ... 还有 {len(units) - 5} 个单位")
    
    # 与预期对比
    expected = {
        "机关部室办公电话": 21,
        "主要生产单位办公电话": 8,
        "主要经营企业办公电话": 3,
        "辅助生产企业办公电话": 6,
        "主要服务单位办公电话": 2,
        "项目经理部办公电话": 1
    }
    
    print(f"\n🎯 与预期结构对比:")
    for expected_category, expected_count in expected.items():
        actual_count = len(category_stats.get(expected_category, []))
        status = "✅" if actual_count == expected_count else "⚠️"
        print(f"   {status} {expected_category}: 预期{expected_count}个，实际{actual_count}个")
    
    print(f"\n🎉 表格链接识别任务完成！")
    print(f"💡 生成的数据可用于构建多层次电话所在单位结构")

if __name__ == "__main__":
    clean_data = clean_and_deduplicate()
    generate_summary()
