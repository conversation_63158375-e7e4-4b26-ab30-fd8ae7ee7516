import csv
import re
import requests
import pandas as pd

CRAWL4AI_API_URL = "http://172.18.151.239:11235/api/crawl"
CSV_FILE_PATH = r"e:\mycode\pc1\newcraper\pc2\final_units_data.csv"
OUTPUT_CSV_PATH = r"e:\mycode\pc1\newcraper\pc2\extracted_phone_data.csv"
TARGET_DEPARTMENT = "行政工作部"

def get_target_url(csv_path, department_name):
    """Reads the CSV file and returns the URL for the target department."""
    try:
        with open(csv_path, mode='r', encoding='utf-8') as infile:
            reader = csv.DictReader(infile)
            for row in reader:
                if row['单位名称'] == department_name:
                    return row['子链接']
        print(f"错误：在 {csv_path} 中未找到单位 '{department_name}'。")
    except FileNotFoundError:
        print(f"错误：CSV文件 {csv_path} 未找到。")
    except Exception as e:
        print(f"读取CSV文件时发生错误：{e}")
    return None

def crawl_page_content(url):
    """Uses crawl4ai to get the HTML content of the page."""
    payload = {
        "url": url,
        # "wait_for": "body", # Example: wait for a specific element if needed
        "timeout": 60000  # Increased timeout to 60 seconds
    }
    try:
        print(f"正在爬取URL: {url}")
        response = requests.post(CRAWL4AI_API_URL, json=payload, timeout=70) # Request timeout slightly higher
        response.raise_for_status()  # Raise an exception for HTTP errors
        data = response.json()
        if 'html' in data:
            print("页面内容爬取成功。")
            return data['html']
        else:
            print(f"错误：crawl4ai的响应中没有'html'字段。响应内容: {data}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"调用crawl4ai API时发生错误：{e}")
    except Exception as e:
        print(f"爬取页面内容时发生未知错误：{e}")
    return None

def extract_phone_info(html_content, department_name):
    """Extracts phone information from HTML content."""
    # Regex to find Chinese names followed by a colon and then a phone number (7 or 11 digits)
    # It will capture the name and the phone number.
    # Example: 张三：1234567 or 李四：13800138000
    # We need to be careful about HTML tags that might be around the text.
    # A simple approach is to first clean up some common HTML tags or use a library for robust parsing.
    # For now, let's try a regex that might work on relatively clean text blocks.
    
    # This regex looks for: (Chinese characters)(colon)(7 or 11 digits)
    # The colon can be full-width (：) or half-width (:).
    pattern = re.compile(r'([\u4e00-\u9fa5]+)\s*[：:]\s*(\d{7,11})')
    
    found_contacts = []
    matches = pattern.findall(html_content)
    
    if not matches:
        print("未找到匹配的电话信息。请检查HTML内容和正则表达式。")
        # Try a broader search if specific pattern fails, e.g. just phone numbers
        # phone_pattern_only = re.compile(r'(\d{7,11})')
        # all_numbers = phone_pattern_only.findall(html_content)
        # print(f"找到的纯数字串: {all_numbers}")

    for name, phone in matches:
        found_contacts.append({
            '电话号码': phone,
            '单位名称': department_name,
            '电话用户名': name.strip()
        })
    
    print(f"从HTML中提取到 {len(found_contacts)} 条电话信息。")
    return found_contacts

def save_to_csv(data, output_path):
    """Saves the extracted data to a CSV file."""
    if not data:
        print("没有数据可保存到CSV。")
        return
    try:
        df = pd.DataFrame(data)
        df.to_csv(output_path, index=False, encoding='utf-8-sig') # utf-8-sig for Excel compatibility
        print(f"数据已成功保存到 {output_path}")
    except Exception as e:
        print(f"保存到CSV时发生错误：{e}")

def main():
    print(f"开始处理单位：{TARGET_DEPARTMENT}")
    target_url = get_target_url(CSV_FILE_PATH, TARGET_DEPARTMENT)
    
    if not target_url:
        return

    html_content = crawl_page_content(target_url)
    
    if not html_content:
        print("未能获取HTML内容，程序终止。")
        return
        
    # For debugging, you can save the raw HTML
    # with open("debug_page_content.html", "w", encoding="utf-8") as f_html:
    #     f_html.write(html_content)
    # print("原始HTML内容已保存到 debug_page_content.html")

    phone_data = extract_phone_info(html_content, TARGET_DEPARTMENT)
    
    save_to_csv(phone_data, OUTPUT_CSV_PATH)
    print("处理完成。")

if __name__ == "__main__":
    main()