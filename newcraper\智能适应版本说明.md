# 🧠 智能适应版本单位提取器说明

## 📋 **改进概述**

原始程序 `final_unit_extractor.py` 已升级为智能适应版本，能够自动适应网页结构变化和单位名称变动。

## 🔄 **主要改进点**

### 1. **智能表格识别** (替代硬编码索引)

**原始问题**：
```python
# 硬编码表格索引，网页结构变化时失效
self.table_mapping = {
    "机关部室办公电话": 11,      # 表格11
    "主要生产单位办公电话": 12,   # 表格12
    ...
}
```

**智能解决方案**：
```python
# 通过内容特征识别表格
self.region_keywords = {
    "机关部室办公电话": ["机关部室", "机关", "部室"],
    "主要生产单位办公电话": ["主要生产单位", "生产单位", "主要生产"],
    ...
}
```

### 2. **多层次识别策略**

程序使用3种方法智能识别表格：

#### 方法1: 标题识别
- 查找表格前的标题文本
- 通过关键词匹配定位表格

#### 方法2: 内容识别  
- 扫描表格内容
- 验证是否包含有效链接

#### 方法3: 模糊匹配
- 分析表格周围文本
- 使用上下文信息识别

### 3. **变化检测系统**

#### 🆕 新增单位检测
```python
# 自动检测新增的单位
for unit_name, unit_data in current_units.items():
    if unit_name not in self.historical_data:
        new_units.append(unit_name)
        logger.info(f"🆕 新增单位: {unit_name}")
```

#### ❌ 删除单位检测
```python
# 自动检测删除的单位
for unit_name in self.historical_data:
    if unit_name not in current_units:
        deleted_units.append(unit_name)
        logger.info(f"❌ 删除单位: {unit_name}")
```

#### 🔄 链接变更检测
```python
# 自动检测链接变更
if old_link != new_link:
    changed_units.append({
        '单位名称': unit_name,
        '旧链接': old_link,
        '新链接': new_link
    })
```

### 4. **历史数据管理**

- **自动保存**: 每次运行后自动保存当前数据
- **变化对比**: 与历史数据对比检测变化
- **报告生成**: 自动生成详细的变化报告

## 🎯 **适应性特点**

### ✅ **网页结构变化适应**
- 表格位置改变 → ✅ 通过内容识别自动适应
- 表格数量变化 → ✅ 动态扫描所有表格
- HTML结构调整 → ✅ 多种识别策略保证成功率

### ✅ **单位名称变动适应**
- 新增单位 → ✅ 自动检测并记录
- 删除单位 → ✅ 自动检测并报告
- 单位改名 → ✅ 检测为删除+新增
- 链接变更 → ✅ 自动检测并对比

### ✅ **容错机制**
- 某个区域缺失 → ✅ 继续处理其他区域
- 识别失败 → ✅ 记录警告但不中断
- 网络异常 → ✅ 完整的错误处理

## 📊 **生成的文件**

### 主要数据文件
- `adaptive_units_data.csv` - 当前提取的单位数据
- `adaptive_units_data.json` - JSON格式数据

### 历史和变化文件
- `units_history.json` - 历史数据基准
- `change_report_YYYYMMDD_HHMMSS.json` - 变化报告
- `adaptive_unit_extractor.log` - 详细执行日志

## 🚀 **使用方法**

### 首次运行
```bash
python newcraper/final_unit_extractor.py
```
- 建立历史数据基准
- 提取当前所有单位信息

### 后续运行
```bash
python newcraper/final_unit_extractor.py
```
- 智能识别表格结构
- 自动检测变化
- 生成变化报告

## 📈 **变化报告示例**

```json
{
  "检测时间": "2024-01-15 14:30:25",
  "新增单位": ["新设立的部门A", "新设立的部门B"],
  "删除单位": ["已撤销的部门C"],
  "变更单位": [
    {
      "单位名称": "某部门",
      "旧链接": "http://172.18.1.16/phone/old_path.html",
      "新链接": "http://172.18.1.16/phone/new_path.html"
    }
  ],
  "变化总数": 4
}
```

## 🔧 **技术优势**

### 1. **智能识别算法**
- 多策略并行识别
- 关键词匹配 + 内容验证
- 上下文分析增强准确性

### 2. **变化检测算法**
- 基于单位名称的精确对比
- 链接变更的自动检测
- 时间戳记录变化历史

### 3. **容错设计**
- 多重备用识别方案
- 部分失败不影响整体
- 详细的日志记录便于调试

## 🎯 **应对各种变化场景**

### 场景1: 新增单位
**情况**: 机关部室新增"数据管理部"
**程序响应**: 
- ✅ 自动检测到新单位
- ✅ 提取新单位的链接
- ✅ 记录到变化报告

### 场景2: 删除单位
**情况**: 某个生产单位被撤销
**程序响应**:
- ✅ 检测到单位缺失
- ✅ 记录删除信息
- ✅ 更新历史数据

### 场景3: 表格位置变化
**情况**: 网页重新设计，表格顺序改变
**程序响应**:
- ✅ 通过内容识别找到正确表格
- ✅ 不依赖固定位置索引
- ✅ 正常提取所有数据

### 场景4: 链接路径变更
**情况**: 服务器重构，链接路径改变
**程序响应**:
- ✅ 检测到链接变更
- ✅ 记录新旧链接对比
- ✅ 自动更新到新链接

## 💡 **使用建议**

### 1. **定期运行**
建议每天或每周运行一次，及时发现变化

### 2. **监控日志**
关注日志中的警告信息，了解识别过程

### 3. **检查报告**
定期查看变化报告，了解单位结构变动

### 4. **备份数据**
保留历史数据文件，便于追溯变化

## 🎉 **总结**

智能适应版本完全解决了原始程序的局限性：

- ❌ **硬编码依赖** → ✅ **智能内容识别**
- ❌ **结构变化失效** → ✅ **多策略适应**
- ❌ **无变化检测** → ✅ **全面变化监控**
- ❌ **单点失败** → ✅ **容错设计**

现在程序能够：
- 🧠 **智能适应**网页结构变化
- 🔍 **自动检测**单位名称变动
- 📊 **生成报告**记录所有变化
- 🛡️ **容错处理**确保稳定运行

**无论网页如何变化，程序都能准确提取单位名称和子链接！**
