#!/usr/bin/env python3
"""
直接运行Firecrawl爬虫的简化版本
"""

import re
import json
import csv
from bs4 import BeautifulSoup

def main():
    print("🔥 使用Firecrawl的矿领导电话表格爬虫")
    print("=" * 60)
    
    try:
        # 导入Firecrawl
        from firecrawl import FirecrawlApp
        print("✅ Firecrawl SDK导入成功")
        
        # 初始化客户端
        api_key = "fc-5cf3a134f43a4a5ca2fd753d1590e8fd"
        app = FirecrawlApp(api_key=api_key)
        print(f"✅ Firecrawl客户端初始化成功")
        
        # 目标URL
        target_url = "http://172.18.1.16/phone/20201227.html"
        print(f"🌐 开始爬取目标URL: {target_url}")
        
        # 爬取网页
        result = app.scrape_url(target_url, formats=['html'])
        
        if result and 'html' in result:
            html_content = result['html']
            print(f"✅ 爬取成功! 内容长度: {len(html_content)} 字符")
            
            # 解析HTML提取电话数据
            soup = BeautifulSoup(html_content, 'html.parser')
            phone_data = []
            
            # 查找表格
            tables = soup.find_all('table')
            print(f"📊 找到 {len(tables)} 个表格")
            
            for table_idx, table in enumerate(tables):
                table_text = table.get_text()
                
                if "电话" in table_text or "矿领导" in table_text or "姓名" in table_text:
                    print(f"✅ 在第{table_idx+1}个表格中找到目标表格")
                    
                    rows = table.find_all('tr')
                    
                    for row_idx, row in enumerate(rows):
                        cells = row.find_all(['td', 'th'])
                        
                        if len(cells) < 2:
                            continue
                        
                        cell_texts = [cell.get_text(strip=True) for cell in cells]
                        row_text = ' '.join(cell_texts)
                        
                        # 查找电话号码
                        phones = re.findall(r'\b\d{7,11}\b', row_text)
                        
                        if phones:
                            print(f"📞 第{row_idx+1}行找到电话: {phones}")
                            
                            for phone in phones:
                                # 查找对应的姓名
                                name = ""
                                phone_cell_idx = -1
                                
                                for idx, cell_text in enumerate(cell_texts):
                                    if phone in cell_text:
                                        phone_cell_idx = idx
                                        break
                                
                                if phone_cell_idx > 0:
                                    name = cell_texts[phone_cell_idx - 1]
                                elif phone_cell_idx == 0 and len(cell_texts) > 1:
                                    name = cell_texts[1]
                                
                                # 清理姓名
                                name = clean_name(name)
                                
                                if is_valid_phone(phone) and name:
                                    record = {
                                        'phone': phone,
                                        'unit': '矿领导',
                                        'name': name,
                                        'extraction_method': 'firecrawl'
                                    }
                                    phone_data.append(record)
                                    print(f"✅ 提取: {phone}、矿领导、{name}")
                    
                    break
            
            # 保存结果
            if phone_data:
                print(f"\n🎉 成功提取 {len(phone_data)} 条电话记录:")
                print("=" * 60)
                
                for i, record in enumerate(phone_data, 1):
                    print(f"{i:2d}. {record['phone']}、{record['unit']}、{record['name']}")
                
                # 保存CSV文件
                csv_file = 'E:/mycode/pc1/firecrawl/phone_data_firecrawl.csv'
                with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                    fieldnames = ['电话号码', '电话所在单位', '用户名']
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    
                    for record in phone_data:
                        writer.writerow({
                            '电话号码': record['phone'],
                            '电话所在单位': record['unit'],
                            '用户名': record['name']
                        })
                
                # 保存JSON文件
                json_file = 'E:/mycode/pc1/firecrawl/phone_data_firecrawl.json'
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(phone_data, f, ensure_ascii=False, indent=2)
                
                print(f"\n💾 数据已保存到:")
                print(f"  - phone_data_firecrawl.csv (中文列名)")
                print(f"  - phone_data_firecrawl.json (完整数据)")
                
                # 按要求格式输出
                print("=" * 60)
                print("📋 按要求格式输出:")
                formatted = []
                for record in phone_data:
                    formatted.append(f"{record['phone']}、{record['unit']}、{record['name']}")
                print("; ".join(formatted))
                
                print("\n🎉 Firecrawl爬虫执行完成！")
                
            else:
                print("❌ 未提取到任何电话数据")
                
        else:
            print("❌ 爬取失败 - 无返回内容")
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

def clean_name(name):
    """清理姓名"""
    if not name:
        return ""
    
    name = re.sub(r'(姓名|联系人|负责人|主任|经理|厂长|部长|科长)', '', name)
    name = re.sub(r'[：:、，,\(\)（）\[\]【】]', '', name)
    name = re.sub(r'\d+', '', name)
    name = name.strip()
    
    if 2 <= len(name) <= 4 and re.match(r'^[\u4e00-\u9fff]+$', name):
        return name
    return ""

def is_valid_phone(phone):
    """验证电话号码"""
    return len(phone) == 7 or len(phone) == 11

if __name__ == "__main__":
    main()
