# 矿领导电话表格爬虫使用说明

## 功能说明
专门爬取 `http://172.18.1.16/phone/20201227.html` 页面中矿领导电话表格的数据。

## 提取结果
成功提取了11条矿领导电话记录：

1. 7719001、矿领导、吴启明
2. 7716398、矿领导、潘斌
3. 7716109、矿领导、耿志强
4. 7719006、矿领导、李建国
5. 7719016、矿领导、周云
6. 7716399、矿领导、周宏
7. 7719998、矿领导、曾流生
8. 7716396、矿领导、王志强
9. 7719025、矿领导、张林水
10. 7719020、矿领导、曾芳
11. 7719018、矿领导、黄满武

## 安装依赖

```bash
pip install -r requirements_simple.txt
```

## 运行程序

```bash
python phone_table_scraper.py
```

## 输出文件

程序运行后会生成以下文件：

1. **phone_table_data.csv** - CSV格式的数据文件
2. **phone_table_data.json** - JSON格式的数据文件  
3. **phone_table_scraper.log** - 详细的运行日志

## 数据格式

### CSV格式
```
phone,unit,name
7719001,矿领导,吴启明
7716398,矿领导,潘斌
...
```

### JSON格式
```json
[
  {
    "phone": "7719001",
    "unit": "矿领导", 
    "name": "吴启明"
  },
  ...
]
```

### 按要求格式输出
```
7719001、矿领导、吴启明; 7716398、矿领导、潘斌; 7716109、矿领导、耿志强; 7719006、矿领导、李建国; 7719016、矿领导、周云; 7716399、矿领导、周宏; 7719998、矿领导、曾流生; 7716396、矿领导、王志强; 7719025、矿领导、张林水; 7719020、矿领导、曾芳; 7719018、矿领导、黄满武
```

## 技术特点

1. **简单高效** - 直接使用HTTP请求，无需复杂的浏览器环境
2. **编码兼容** - 自动处理UTF-8、GBK、GB2312等中文编码
3. **智能解析** - 自动识别表格结构，准确提取姓名和电话对应关系
4. **数据验证** - 验证电话号码格式和姓名有效性
5. **多格式输出** - 同时生成CSV、JSON和格式化文本输出
6. **详细日志** - 完整记录爬取过程，便于调试

## 程序逻辑

1. **连接测试** - 首先测试目标网页的可访问性
2. **内容获取** - 使用多种编码方式获取网页内容
3. **表格识别** - 查找包含"矿领导"关键词的表格
4. **数据提取** - 使用正则表达式提取电话号码，通过位置关系匹配姓名
5. **数据清理** - 清理和验证提取的姓名和电话数据
6. **结果输出** - 生成多种格式的输出文件

## 注意事项

- 程序只提取7位或11位的有效电话号码
- 姓名必须是2-4个中文字符
- 所有日志信息只写入日志文件，不在控制台显示错误信息
- 程序会自动处理网页编码问题

## 扩展性

如果需要爬取其他表格或页面，可以修改以下部分：
- `target_url` - 目标网页地址
- `extract_phone_table()` - 表格识别和解析逻辑
- `clean_name()` - 姓名清理规则
- `is_valid_phone()` - 电话号码验证规则
