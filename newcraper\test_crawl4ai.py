#!/usr/bin/env python3
"""
测试本地部署的crawl4ai服务连接
"""

import asyncio
import aiohttp

async def test_crawl4ai_health():
    """测试crawl4ai健康状态"""
    crawl4ai_url = "http://172.18.151.239:11235"

    try:
        async with aiohttp.ClientSession() as session:
            health_url = f"{crawl4ai_url}/health"
            print(f"测试crawl4ai健康检查: {health_url}")

            async with session.get(health_url, timeout=10) as response:
                if response.status == 200:
                    result = await response.text()
                    print("SUCCESS: crawl4ai服务健康检查通过")
                    print(f"响应: {result}")
                    return True
                else:
                    print(f"ERROR: 健康检查失败，状态码: {response.status}")
                    return False
    except Exception as e:
        print(f"ERROR: 连接crawl4ai服务失败: {e}")
        return False

async def test_crawl4ai_api():
    """测试crawl4ai API功能"""
    crawl4ai_url = "http://172.18.151.239:11235"
    api_token = "123456"

    try:
        async with aiohttp.ClientSession() as session:
            api_url = f"{crawl4ai_url}/crawl"
            headers = {
                'Authorization': f'Bearer {api_token}',
                'Content-Type': 'application/json'
            }

            # 测试用简单网页
            payload = {
                'urls': ['https://httpbin.org/html'],
                'extraction_strategy': 'NoExtractionStrategy',
                'word_count_threshold': 10,
                'bypass_cache': True
            }

            print(f"测试crawl4ai API: {api_url}")
            print(f"测试URL: {payload['urls'][0]}")

            async with session.post(api_url, json=payload, headers=headers, timeout=30) as response:
                if response.status == 200:
                    result = await response.json()
                    print("SUCCESS: crawl4ai API测试成功")
                    print(f"返回数据键: {list(result.keys())}")

                    if result.get('success') and result.get('results'):
                        print("SUCCESS: 爬取成功")
                        first_result = result['results'][0]
                        html_content = first_result.get('cleaned_html') or first_result.get('raw_html') or first_result.get('html', '')
                        print(f"获取内容长度: {len(html_content)} 字符")
                        return True
                    else:
                        print(f"ERROR: 爬取失败: {result.get('error', 'Unknown error')}")
                        return False
                else:
                    error_text = await response.text()
                    print(f"ERROR: API请求失败，状态码: {response.status}")
                    print(f"错误信息: {error_text}")
                    return False

    except Exception as e:
        print(f"ERROR: crawl4ai API测试异常: {e}")
        return False

async def test_target_url():
    """测试目标网页爬取"""
    crawl4ai_url = "http://172.18.151.239:11235"
    api_token = "123456"
    target_url = "http://172.18.1.16/phone/20201227.html"

    try:
        async with aiohttp.ClientSession() as session:
            api_url = f"{crawl4ai_url}/crawl"
            headers = {
                'Authorization': f'Bearer {api_token}',
                'Content-Type': 'application/json'
            }

            payload = {
                'urls': [target_url],
                'extraction_strategy': 'NoExtractionStrategy',
                'word_count_threshold': 10,
                'bypass_cache': True,
                'include_raw_html': True
            }

            print(f"测试目标网页爬取: {target_url}")

            async with session.post(api_url, json=payload, headers=headers, timeout=60) as response:
                if response.status == 200:
                    result = await response.json()

                    if result.get('success') and result.get('results'):
                        first_result = result['results'][0]
                        html_content = first_result.get('cleaned_html') or first_result.get('raw_html') or first_result.get('html', '')
                        print("SUCCESS: 目标网页爬取成功")
                        print(f"获取内容长度: {len(html_content)} 字符")

                        # 检查是否包含电话相关内容
                        if "电话" in html_content or "矿领导" in html_content:
                            print("SUCCESS: 发现电话相关内容")
                        else:
                            print("WARNING: 未发现电话相关内容")

                        return True
                    else:
                        print(f"ERROR: 目标网页爬取失败: {result.get('error', 'Unknown error')}")
                        return False
                else:
                    error_text = await response.text()
                    print(f"ERROR: 目标网页爬取请求失败，状态码: {response.status}")
                    print(f"错误信息: {error_text}")
                    return False

    except Exception as e:
        print(f"ERROR: 目标网页爬取异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("crawl4ai服务连接测试")
    print("=" * 40)

    tests = [
        ("健康检查", test_crawl4ai_health),
        ("API功能测试", test_crawl4ai_api),
        ("目标网页爬取", test_target_url)
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"\n{test_name}...")
        print("-" * 30)

        try:
            result = await test_func()
            results[test_name] = result

            if result:
                print(f"✓ {test_name}: 通过")
            else:
                print(f"✗ {test_name}: 失败")

        except Exception as e:
            print(f"✗ {test_name}: 异常 - {e}")
            results[test_name] = False

    # 测试结果汇总
    print("\n" + "=" * 40)
    print("测试结果汇总:")

    passed = sum(results.values())
    total = len(results)

    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")

    print(f"\n总计: {passed}/{total} 项测试通过")

    if passed == total:
        print("SUCCESS: 所有测试通过！可以运行爬虫程序。")
    else:
        print("ERROR: 部分测试失败，请检查crawl4ai服务配置。")

    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
