#!/usr/bin/env python3
"""
使用本地部署的crawl4ai爬取矿领导电话表格
目标：http://172.18.1.16/phone/20201227.html 中的矿领导电话表格
crawl4ai地址：172.18.151.239:11235
格式：电话号码、所在单位、用户名
"""

import asyncio
import re
import json
import csv
import logging
from typing import List, Dict, Optional
import aiohttp
from bs4 import BeautifulSoup

# 配置日志 - 只输出到文件
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phone_scraper_crawl4ai.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class PhoneScraperCrawl4AI:
    def __init__(self,
                 target_url: str = "http://172.18.1.16/phone/20201227.html",
                 crawl4ai_url: str = "http://172.18.151.239:11235"):
        self.target_url = target_url
        self.crawl4ai_url = crawl4ai_url
        self.api_token = "123456"
        self.phone_data = []

        # 电话号码匹配模式
        self.phone_pattern = r'\b\d{7,11}\b'

    async def test_crawl4ai_connection(self) -> bool:
        """测试crawl4ai服务连接"""
        try:
            async with aiohttp.ClientSession() as session:
                health_url = f"{self.crawl4ai_url}/health"
                async with session.get(health_url, timeout=10) as response:
                    if response.status == 200:
                        logger.info("SUCCESS: crawl4ai服务连接成功")
                        return True
                    else:
                        logger.error(f"ERROR: crawl4ai健康检查失败: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"ERROR: 无法连接到crawl4ai服务: {e}")
            return False

    async def crawl_with_crawl4ai(self, url: str) -> Optional[str]:
        """使用crawl4ai API爬取网页"""
        try:
            async with aiohttp.ClientSession() as session:
                api_url = f"{self.crawl4ai_url}/crawl"
                headers = {
                    'Authorization': f'Bearer {self.api_token}',
                    'Content-Type': 'application/json'
                }

                # crawl4ai API请求参数
                payload = {
                    'urls': [url],
                    'bypass_cache': True,
                    'include_raw_html': True
                }

                logger.info(f"INFO: 使用crawl4ai爬取: {url}")

                async with session.post(api_url, json=payload, headers=headers, timeout=60) as response:
                    if response.status == 200:
                        result = await response.json()

                        # 检查是否是异步任务
                        if 'task_id' in result:
                            task_id = result['task_id']
                            logger.info(f"INFO: 获得异步任务ID: {task_id}")

                            # 轮询任务状态
                            for attempt in range(30):  # 最多等待30次，每次2秒
                                await asyncio.sleep(2)

                                status_url = f"{self.crawl4ai_url}/task/{task_id}"
                                async with session.get(status_url, headers=headers, timeout=10) as status_response:
                                    if status_response.status == 200:
                                        status_result = await status_response.json()

                                        if status_result.get('status') == 'completed':
                                            logger.info("SUCCESS: 异步任务完成")

                                            # 获取结果 - 修复结果解析逻辑
                                            html_content = None

                                            # 尝试多种结果路径
                                            if status_result.get('result') and status_result['result'].get('results'):
                                                first_result = status_result['result']['results'][0]
                                                html_content = (first_result.get('cleaned_html') or
                                                              first_result.get('raw_html') or
                                                              first_result.get('html', ''))
                                            elif status_result.get('results'):
                                                first_result = status_result['results'][0]
                                                html_content = (first_result.get('cleaned_html') or
                                                              first_result.get('raw_html') or
                                                              first_result.get('html', ''))

                                            if html_content:
                                                logger.info("SUCCESS: crawl4ai成功获取网页内容")
                                                logger.info(f"DEBUG: 内容长度: {len(html_content)} 字符")
                                                return html_content
                                            else:
                                                logger.error("ERROR: 任务完成但无法找到HTML内容")
                                                logger.error(f"DEBUG: 状态结果键: {list(status_result.keys())}")
                                                return None

                                        elif status_result.get('status') == 'failed':
                                            logger.error(f"ERROR: 异步任务失败: {status_result.get('error', 'Unknown error')}")
                                            return None

                                        elif status_result.get('status') in ['pending', 'running']:
                                            logger.info(f"INFO: 任务状态: {status_result.get('status')}, 继续等待...")
                                            continue

                                        else:
                                            logger.warning(f"WARNING: 未知任务状态: {status_result.get('status')}")
                                            continue
                                    else:
                                        logger.warning(f"WARNING: 无法获取任务状态: {status_response.status}")
                                        continue

                            logger.error("ERROR: 异步任务超时")
                            return None

                        # 检查同步返回结果
                        elif result.get('success') and result.get('results'):
                            # 获取第一个结果
                            first_result = result['results'][0]

                            # 尝试获取cleaned_html或raw_html
                            html_content = (first_result.get('cleaned_html') or
                                          first_result.get('raw_html') or
                                          first_result.get('html', ''))

                            if html_content:
                                logger.info("SUCCESS: crawl4ai成功获取网页内容")
                                logger.info(f"DEBUG: 内容长度: {len(html_content)} 字符")
                                return html_content
                            else:
                                logger.error("ERROR: crawl4ai返回空内容")
                                logger.error(f"DEBUG: 结果键: {list(first_result.keys())}")
                                return None
                        else:
                            error_msg = result.get('error', 'Unknown error')
                            logger.error(f"ERROR: crawl4ai爬取失败: {error_msg}")
                            logger.error(f"DEBUG: 完整响应: {result}")
                            return None
                    else:
                        error_text = await response.text()
                        logger.error(f"ERROR: crawl4ai API请求失败: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.error(f"ERROR: crawl4ai爬取异常: {e}")
            return None

    def extract_phone_table(self, html: str) -> List[Dict]:
        """提取矿领导电话表格数据"""
        soup = BeautifulSoup(html, 'html.parser')
        extracted_data = []

        logger.info("INFO: 开始解析HTML内容，查找矿领导电话表格")

        # 查找包含"矿领导"的表格
        tables = soup.find_all('table')
        logger.info(f"INFO: 找到 {len(tables)} 个表格")

        for table_idx, table in enumerate(tables):
            # 检查表格是否包含矿领导相关内容
            table_text = table.get_text()
            if "矿领导" in table_text or "姓名" in table_text or "办公室电话" in table_text:
                logger.info(f"FOUND: 在第{table_idx+1}个表格中找到矿领导电话表格")

                rows = table.find_all('tr')
                logger.info(f"INFO: 表格共有 {len(rows)} 行")

                for row_idx, row in enumerate(rows):
                    cells = row.find_all(['td', 'th'])

                    # 跳过空行或表头行
                    if len(cells) < 2:
                        continue

                    # 提取每行的所有文本
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    row_text = ' '.join(cell_texts)

                    # 查找电话号码
                    phones = re.findall(self.phone_pattern, row_text)

                    if phones:
                        logger.info(f"PROCESSING: 第{row_idx+1}行找到电话: {phones}")
                        logger.info(f"DEBUG: 行内容: {row_text}")

                        # 对每个电话号码进行处理
                        for phone in phones:
                            # 查找电话号码所在的单元格位置
                            phone_cell_idx = -1
                            for idx, cell_text in enumerate(cell_texts):
                                if phone in cell_text:
                                    phone_cell_idx = idx
                                    break

                            # 提取姓名（通常在电话号码前面的单元格）
                            name = ""
                            if phone_cell_idx > 0:
                                # 从前一个单元格获取姓名
                                name = cell_texts[phone_cell_idx - 1]
                            elif phone_cell_idx == 0 and len(cell_texts) > 1:
                                # 如果电话在第一列，姓名可能在第二列
                                name = cell_texts[1]

                            # 清理姓名
                            name = self.clean_name(name)

                            # 确定所在单位
                            unit = "矿领导"  # 根据需求，这个表格都是矿领导

                            # 验证数据有效性
                            if self.is_valid_phone(phone) and name:
                                phone_record = {
                                    'phone': phone,
                                    'unit': unit,
                                    'name': name,
                                    'row_data': row_text,  # 保存原始行数据用于调试
                                    'extraction_method': 'crawl4ai'
                                }
                                extracted_data.append(phone_record)
                                logger.info(f"EXTRACTED: {phone}、{unit}、{name}")
                            else:
                                logger.warning(f"WARNING: 跳过无效数据 - 电话:{phone}, 姓名:{name}")

                break  # 找到目标表格后退出循环

        if not extracted_data:
            logger.warning("WARNING: 未找到任何有效的电话数据")

        return extracted_data

    def clean_name(self, name: str) -> str:
        """清理姓名数据"""
        if not name:
            return ""

        # 移除常见的非姓名文本
        name = re.sub(r'(姓名|联系人|负责人|主任|经理|厂长|部长|科长)', '', name)
        name = re.sub(r'[：:、，,\(\)（）\[\]【】]', '', name)
        name = re.sub(r'\d+', '', name)  # 移除数字
        name = name.strip()

        # 验证是否为有效姓名（2-4个中文字符）
        if 2 <= len(name) <= 4 and re.match(r'^[\u4e00-\u9fff]+$', name):
            return name

        return ""

    def is_valid_phone(self, phone: str) -> bool:
        """验证电话号码有效性"""
        # 7位内线电话或11位手机号码
        return len(phone) == 7 or len(phone) == 11

    async def scrape_phone_table(self) -> List[Dict]:
        """主要爬取函数"""
        logger.info("START: 开始使用crawl4ai爬取矿领导电话表格")

        # 测试crawl4ai连接
        if not await self.test_crawl4ai_connection():
            logger.error("ERROR: 无法连接到crawl4ai服务，请检查服务是否正常运行")
            return []

        # 使用crawl4ai获取网页内容
        html_content = await self.crawl_with_crawl4ai(self.target_url)
        if not html_content:
            logger.error("ERROR: crawl4ai无法获取网页内容")
            return []

        # 提取表格数据
        self.phone_data = self.extract_phone_table(html_content)

        logger.info(f"COMPLETED: 使用crawl4ai爬取完成，共提取 {len(self.phone_data)} 条电话记录")
        return self.phone_data

    def save_to_csv(self, filename: str = "phone_table_crawl4ai.csv"):
        """保存数据到CSV文件"""
        if not self.phone_data:
            logger.warning("WARNING: 没有数据可保存")
            return

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['phone', 'unit', 'name', 'extraction_method']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for record in self.phone_data:
                writer.writerow({
                    'phone': record['phone'],
                    'unit': record['unit'],
                    'name': record['name'],
                    'extraction_method': record['extraction_method']
                })

        logger.info(f"SAVED: 数据已保存到 {filename}")

    def save_to_json(self, filename: str = "phone_table_crawl4ai.json"):
        """保存数据到JSON文件"""
        if not self.phone_data:
            logger.warning("WARNING: 没有数据可保存")
            return

        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.phone_data, jsonfile, ensure_ascii=False, indent=2)

        logger.info(f"SAVED: 数据已保存到 {filename}")

    def print_results(self):
        """打印结果"""
        if not self.phone_data:
            print("ERROR: 没有提取到数据")
            return

        print(f"\n使用crawl4ai提取的矿领导电话表格数据 (共 {len(self.phone_data)} 条记录):")
        print("=" * 70)

        for i, record in enumerate(self.phone_data, 1):
            print(f"{i:2d}. {record['phone']}、{record['unit']}、{record['name']}")

        print("=" * 70)

        # 按您要求的格式输出
        print("\n按要求格式输出:")
        formatted_output = []
        for record in self.phone_data:
            formatted_output.append(f"{record['phone']}、{record['unit']}、{record['name']}")

        print("; ".join(formatted_output))

        print(f"\n提取方法: crawl4ai (服务地址: {self.crawl4ai_url})")


async def main():
    """主函数"""
    print("矿领导电话表格爬虫 (使用crawl4ai)")
    print("=" * 50)
    print(f"crawl4ai服务地址: 172.18.151.239:11235")
    print(f"目标网页: http://172.18.1.16/phone/20201227.html")
    print("=" * 50)

    scraper = PhoneScraperCrawl4AI()

    try:
        # 执行爬取
        data = await scraper.scrape_phone_table()

        if data:
            # 显示结果
            scraper.print_results()

            # 保存数据
            scraper.save_to_csv()
            scraper.save_to_json()

            print(f"\n数据文件已保存到当前目录:")
            print(f"  - phone_table_crawl4ai.csv")
            print(f"  - phone_table_crawl4ai.json")
            print(f"  - phone_scraper_crawl4ai.log")
        else:
            print("ERROR: 没有提取到任何数据，请检查:")
            print("  1. crawl4ai服务是否正常运行")
            print("  2. 目标网页是否可访问")
            print("  3. 查看日志文件获取详细错误信息")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"ERROR: 程序执行失败: {e}")
        logger.error(f"ERROR: 程序执行失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
