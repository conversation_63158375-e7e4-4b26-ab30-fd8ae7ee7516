#!/usr/bin/env python3
"""
使用Firecrawl技术的矿领导电话表格爬虫
目标：http://172.18.1.16/phone/20201227.html 中的矿领导电话表格
技术：Firecrawl API + Python SDK
格式：电话号码、电话所在单位、用户名
"""

import os
import re
import json
import csv
import logging
import asyncio
from typing import List, Dict, Optional
from bs4 import BeautifulSoup

# 配置日志 - 只输出到文件
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('E:/mycode/pc1/firecrawl/phone_scraper_firecrawl.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class PhoneScraperFirecrawl:
    """
    使用Firecrawl的矿领导电话表格爬虫类

    主要功能：
    1. 使用Firecrawl API爬取指定网页
    2. 解析HTML内容提取电话号码、单位和姓名
    3. 保存结果到CSV和JSON文件
    4. 支持同步和异步两种模式
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        初始化Firecrawl爬虫

        Args:
            api_key: Firecrawl API密钥，如果不提供则从环境变量获取
        """
        # 目标网页地址
        self.target_url = "http://172.18.1.16/phone/20201227.html"

        # 存储提取的电话数据
        self.phone_data = []

        # 电话号码正则表达式：匹配7位内线或11位手机号
        self.phone_pattern = r'\b\d{7,11}\b'

        # 初始化Firecrawl客户端
        self.firecrawl_app = None
        self.async_firecrawl_app = None

        # 设置API密钥
        self.api_key = api_key or os.getenv('FIRECRAWL_API_KEY')

        if not self.api_key:
            logger.warning("WARNING: 未设置Firecrawl API密钥，将使用演示模式")
            print("WARNING: 未设置Firecrawl API密钥")
            print("请设置环境变量 FIRECRAWL_API_KEY 或在初始化时传入api_key参数")

        # 初始化Firecrawl客户端
        self._init_firecrawl_clients()

    def _init_firecrawl_clients(self):
        """初始化Firecrawl客户端（同步和异步）"""
        try:
            # 尝试导入Firecrawl SDK
            from firecrawl import FirecrawlApp, AsyncFirecrawlApp, ScrapeOptions

            if self.api_key:
                # 初始化同步客户端
                self.firecrawl_app = FirecrawlApp(api_key=self.api_key)

                # 初始化异步客户端
                self.async_firecrawl_app = AsyncFirecrawlApp(api_key=self.api_key)

                logger.info("SUCCESS: Firecrawl客户端初始化成功")
                print("SUCCESS: Firecrawl客户端初始化成功")
            else:
                logger.error("ERROR: 无法初始化Firecrawl客户端，缺少API密钥")
                print("ERROR: 无法初始化Firecrawl客户端，缺少API密钥")

        except ImportError as e:
            logger.error(f"ERROR: 无法导入Firecrawl SDK: {e}")
            print(f"ERROR: 无法导入Firecrawl SDK: {e}")
            print("请安装Firecrawl SDK: pip install firecrawl-py")
            self.firecrawl_app = None
            self.async_firecrawl_app = None

    def test_firecrawl_connection(self) -> bool:
        """
        测试Firecrawl服务连接

        Returns:
            bool: 连接成功返回True，失败返回False
        """
        if not self.firecrawl_app:
            logger.error("ERROR: Firecrawl客户端未初始化")
            print("ERROR: Firecrawl客户端未初始化")
            return False

        try:
            # 使用一个简单的网页测试连接
            test_url = "https://httpbin.org/html"
            logger.info(f"INFO: 测试Firecrawl连接，使用测试URL: {test_url}")
            print(f"INFO: 测试Firecrawl连接...")

            # 尝试爬取测试页面
            result = self.firecrawl_app.scrape_url(
                test_url,
                formats=['markdown', 'html']
            )

            if result and 'markdown' in result:
                logger.info("SUCCESS: Firecrawl连接测试成功")
                print("SUCCESS: Firecrawl连接测试成功")
                return True
            else:
                logger.error("ERROR: Firecrawl连接测试失败，无返回数据")
                print("ERROR: Firecrawl连接测试失败")
                return False

        except Exception as e:
            logger.error(f"ERROR: Firecrawl连接测试异常: {e}")
            print(f"ERROR: Firecrawl连接测试异常: {e}")
            return False

    def scrape_with_firecrawl(self) -> Optional[str]:
        """
        使用Firecrawl爬取目标网页

        Returns:
            Optional[str]: 成功返回HTML内容，失败返回None
        """
        if not self.firecrawl_app:
            logger.error("ERROR: Firecrawl客户端未初始化")
            return None

        try:
            logger.info(f"INFO: 使用Firecrawl爬取目标网页: {self.target_url}")
            print(f"INFO: 使用Firecrawl爬取目标网页...")

            # 使用Firecrawl爬取网页，获取HTML和Markdown格式
            result = self.firecrawl_app.scrape_url(
                self.target_url,
                formats=['html', 'markdown']
            )

            if result:
                # 优先使用HTML内容，如果没有则使用Markdown
                html_content = result.get('html') or result.get('markdown', '')

                if html_content:
                    logger.info("SUCCESS: Firecrawl成功获取网页内容")
                    logger.info(f"DEBUG: 内容长度: {len(html_content)} 字符")
                    print("SUCCESS: Firecrawl成功获取网页内容")
                    print(f"内容长度: {len(html_content)} 字符")
                    return html_content
                else:
                    logger.error("ERROR: Firecrawl返回空内容")
                    print("ERROR: Firecrawl返回空内容")
                    return None
            else:
                logger.error("ERROR: Firecrawl爬取失败，无返回结果")
                print("ERROR: Firecrawl爬取失败")
                return None

        except Exception as e:
            logger.error(f"ERROR: Firecrawl爬取异常: {e}")
            print(f"ERROR: Firecrawl爬取异常: {e}")
            return None

    async def async_scrape_with_firecrawl(self) -> Optional[str]:
        """
        使用Firecrawl异步爬取目标网页

        Returns:
            Optional[str]: 成功返回HTML内容，失败返回None
        """
        if not self.async_firecrawl_app:
            logger.error("ERROR: Firecrawl异步客户端未初始化")
            return None

        try:
            logger.info(f"INFO: 使用Firecrawl异步爬取目标网页: {self.target_url}")
            print(f"INFO: 使用Firecrawl异步爬取目标网页...")

            # 使用异步Firecrawl爬取网页
            result = await self.async_firecrawl_app.scrape_url(
                self.target_url,
                formats=['html', 'markdown']
            )

            if result:
                # 优先使用HTML内容，如果没有则使用Markdown
                html_content = result.get('html') or result.get('markdown', '')

                if html_content:
                    logger.info("SUCCESS: Firecrawl异步爬取成功")
                    logger.info(f"DEBUG: 内容长度: {len(html_content)} 字符")
                    print("SUCCESS: Firecrawl异步爬取成功")
                    print(f"内容长度: {len(html_content)} 字符")
                    return html_content
                else:
                    logger.error("ERROR: Firecrawl异步爬取返回空内容")
                    print("ERROR: Firecrawl异步爬取返回空内容")
                    return None
            else:
                logger.error("ERROR: Firecrawl异步爬取失败")
                print("ERROR: Firecrawl异步爬取失败")
                return None

        except Exception as e:
            logger.error(f"ERROR: Firecrawl异步爬取异常: {e}")
            print(f"ERROR: Firecrawl异步爬取异常: {e}")
            return None

    def extract_phone_data(self, html_content: str) -> List[Dict]:
        """
        从HTML内容中提取电话数据

        Args:
            html_content: 网页HTML内容

        Returns:
            List[Dict]: 提取的电话数据列表，每个字典包含phone、unit、name字段
        """
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        extracted = []

        logger.info("INFO: 开始解析HTML内容提取电话数据")
        print("INFO: 开始解析HTML内容提取电话数据")

        # 查找页面中的所有表格
        tables = soup.find_all('table')
        logger.info(f"INFO: 找到 {len(tables)} 个表格")
        print(f"INFO: 找到 {len(tables)} 个表格")

        # 遍历所有表格，查找包含矿领导电话信息的表格
        for table_idx, table in enumerate(tables):
            table_text = table.get_text()

            # 通过关键词识别目标表格
            if "矿领导" in table_text or "姓名" in table_text or "办公室电话" in table_text:
                logger.info(f"FOUND: 在第{table_idx+1}个表格中找到目标表格")
                print(f"FOUND: 在第{table_idx+1}个表格中找到目标表格")

                # 获取表格中的所有行
                rows = table.find_all('tr')

                # 遍历表格的每一行
                for row_idx, row in enumerate(rows):
                    # 获取行中的所有单元格
                    cells = row.find_all(['td', 'th'])

                    # 跳过空行或单元格数量过少的行
                    if len(cells) < 2:
                        continue

                    # 提取每个单元格的文本内容
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    row_text = ' '.join(cell_texts)

                    # 使用正则表达式查找电话号码
                    phones = re.findall(self.phone_pattern, row_text)

                    # 如果找到电话号码，进行进一步处理
                    if phones:
                        logger.info(f"PROCESSING: 第{row_idx+1}行找到电话: {phones}")
                        print(f"PROCESSING: 第{row_idx+1}行找到电话: {phones}")

                        # 处理每个找到的电话号码
                        for phone in phones:
                            # 查找与电话号码对应的姓名
                            name = ""
                            phone_cell_idx = -1

                            # 找到包含电话号码的单元格位置
                            for idx, cell_text in enumerate(cell_texts):
                                if phone in cell_text:
                                    phone_cell_idx = idx
                                    break

                            # 根据电话号码位置推断姓名位置
                            if phone_cell_idx > 0:
                                # 电话号码在后面，姓名通常在前一个单元格
                                name = cell_texts[phone_cell_idx - 1]
                            elif phone_cell_idx == 0 and len(cell_texts) > 1:
                                # 电话号码在第一列，姓名可能在第二列
                                name = cell_texts[1]

                            # 清理和验证姓名
                            name = self.clean_name(name)

                            # 验证电话号码和姓名的有效性
                            if self.is_valid_phone(phone) and name:
                                # 创建电话记录
                                record = {
                                    'phone': phone,
                                    'unit': '矿领导',  # 固定为矿领导
                                    'name': name,
                                    'extraction_method': 'firecrawl'  # 标记提取方法
                                }
                                extracted.append(record)
                                logger.info(f"EXTRACTED: {phone}、矿领导、{name}")
                                print(f"EXTRACTED: {phone}、矿领导、{name}")

                break  # 找到目标表格后退出循环

        return extracted

    def clean_name(self, name: str) -> str:
        """
        清理和验证姓名字符串

        Args:
            name: 原始姓名字符串

        Returns:
            str: 清理后的姓名，无效时返回空字符串
        """
        if not name:
            return ""

        # 移除常见的职务和标识词
        name = re.sub(r'(姓名|联系人|负责人|主任|经理|厂长|部长|科长)', '', name)

        # 移除标点符号
        name = re.sub(r'[：:、，,\(\)（）\[\]【】]', '', name)

        # 移除数字
        name = re.sub(r'\d+', '', name)

        # 去除首尾空格
        name = name.strip()

        # 验证姓名格式：2-4个中文字符
        if 2 <= len(name) <= 4 and re.match(r'^[\u4e00-\u9fff]+$', name):
            return name
        return ""

    def is_valid_phone(self, phone: str) -> bool:
        """
        验证电话号码格式

        Args:
            phone: 电话号码字符串

        Returns:
            bool: 有效返回True，无效返回False
        """
        # 只接受7位内线电话或11位手机号码
        return len(phone) == 7 or len(phone) == 11

    def save_results(self):
        """保存结果到CSV和JSON文件"""
        # 保存CSV文件 - 使用中文列名，不包含extraction_method列
        csv_file = 'E:/mycode/pc1/firecrawl/phone_data_firecrawl.csv'
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            # 定义中文列名
            fieldnames = ['电话号码', '电话所在单位', '用户名']
            writer = csv.DictWriter(f, fieldnames=fieldnames)

            # 写入表头
            writer.writeheader()

            # 写入数据行，映射字段名
            for record in self.phone_data:
                writer.writerow({
                    '电话号码': record['phone'],
                    '电话所在单位': record['unit'],
                    '用户名': record['name']
                })

        # 保存JSON文件 - 保持原有格式用于程序处理
        json_file = 'E:/mycode/pc1/firecrawl/phone_data_firecrawl.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.phone_data, f, ensure_ascii=False, indent=2)

        logger.info(f"SAVED: 数据已保存到 {csv_file} 和 {json_file}")
        print(f"\n数据已保存到:")
        print(f"  - phone_data_firecrawl.csv (中文列名)")
        print(f"  - phone_data_firecrawl.json (完整数据)")
        print(f"  - phone_scraper_firecrawl.log (详细日志)")

    def run_scraper_sync(self):
        """
        运行同步爬虫的主要方法

        执行流程：
        1. 测试Firecrawl连接
        2. 使用Firecrawl爬取网页内容
        3. 解析HTML提取电话数据
        4. 保存结果到文件
        """
        print("使用Firecrawl的矿领导电话表格爬虫 (同步版本)")
        print("=" * 60)

        # 第一步：测试Firecrawl服务连接
        if not self.test_firecrawl_connection():
            print("ERROR: 无法连接到Firecrawl服务")
            return

        # 第二步：使用Firecrawl爬取网页内容
        html_content = self.scrape_with_firecrawl()

        # 检查是否成功获取网页内容
        if not html_content:
            print("ERROR: 无法获取网页内容")
            return

        # 第三步：从HTML内容中提取电话数据
        self.phone_data = self.extract_phone_data(html_content)

        # 第四步：处理和显示提取结果
        if self.phone_data:
            print(f"\n成功提取 {len(self.phone_data)} 条电话记录:")
            print("=" * 60)

            # 显示每条记录的详细信息
            for i, record in enumerate(self.phone_data, 1):
                print(f"{i:2d}. {record['phone']}、{record['unit']}、{record['name']}")

            # 第五步：保存数据到文件
            self.save_results()

            # 按要求的格式输出所有数据
            print("=" * 60)
            print("按要求格式输出:")
            formatted = []
            for record in self.phone_data:
                formatted.append(f"{record['phone']}、{record['unit']}、{record['name']}")
            print("; ".join(formatted))

        else:
            print("ERROR: 未提取到任何电话数据")

    async def run_scraper_async(self):
        """
        运行异步爬虫的主要方法

        执行流程：
        1. 测试Firecrawl连接
        2. 使用Firecrawl异步爬取网页内容
        3. 解析HTML提取电话数据
        4. 保存结果到文件
        """
        print("使用Firecrawl的矿领导电话表格爬虫 (异步版本)")
        print("=" * 60)

        # 第一步：测试Firecrawl服务连接
        if not self.test_firecrawl_connection():
            print("ERROR: 无法连接到Firecrawl服务")
            return

        # 第二步：使用Firecrawl异步爬取网页内容
        html_content = await self.async_scrape_with_firecrawl()

        # 检查是否成功获取网页内容
        if not html_content:
            print("ERROR: 无法获取网页内容")
            return

        # 第三步：从HTML内容中提取电话数据
        self.phone_data = self.extract_phone_data(html_content)

        # 第四步：处理和显示提取结果
        if self.phone_data:
            print(f"\n成功提取 {len(self.phone_data)} 条电话记录:")
            print("=" * 60)

            # 显示每条记录的详细信息
            for i, record in enumerate(self.phone_data, 1):
                print(f"{i:2d}. {record['phone']}、{record['unit']}、{record['name']}")

            # 第五步：保存数据到文件
            self.save_results()

            # 按要求的格式输出所有数据
            print("=" * 60)
            print("按要求格式输出:")
            formatted = []
            for record in self.phone_data:
                formatted.append(f"{record['phone']}、{record['unit']}、{record['name']}")
            print("; ".join(formatted))

        else:
            print("ERROR: 未提取到任何电话数据")


def main_sync():
    """
    同步版本的程序入口函数
    """
    # 创建爬虫实例 - 可以传入API密钥或使用环境变量
    scraper = PhoneScraperFirecrawl()

    # 运行同步爬虫
    scraper.run_scraper_sync()


async def main_async():
    """
    异步版本的程序入口函数
    """
    # 创建爬虫实例 - 可以传入API密钥或使用环境变量
    scraper = PhoneScraperFirecrawl()

    # 运行异步爬虫
    await scraper.run_scraper_async()


if __name__ == "__main__":
    """
    程序启动点

    支持同步和异步两种运行模式
    """
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--async":
        print("启动异步模式...")
        asyncio.run(main_async())
    else:
        print("启动同步模式...")
        main_sync()
