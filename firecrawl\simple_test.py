#!/usr/bin/env python3
"""
简单的Firecrawl API测试
"""

def test_firecrawl():
    try:
        print("🔍 测试Firecrawl API...")
        
        # 导入Firecrawl
        from firecrawl import FirecrawlApp
        print("✅ Firecrawl SDK导入成功")
        
        # 初始化客户端
        api_key = "fc-5cf3a134f43a4a5ca2fd753d1590e8fd"
        app = FirecrawlApp(api_key=api_key)
        print(f"✅ Firecrawl客户端初始化成功 (API密钥长度: {len(api_key)})")
        
        # 测试简单网页
        test_url = "https://httpbin.org/html"
        print(f"🌐 测试爬取: {test_url}")
        
        result = app.scrape_url(test_url, formats=['html'])
        
        if result and 'html' in result:
            html_content = result['html']
            print(f"✅ 爬取成功! 内容长度: {len(html_content)} 字符")
            
            # 检查内容
            if "Herman Melville" in html_content:
                print("✅ 内容验证成功 - 找到预期文本")
            else:
                print("⚠️ 内容验证失败 - 未找到预期文本")
            
            return True
        else:
            print("❌ 爬取失败 - 无返回内容")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_target_url():
    try:
        print("\n🎯 测试目标URL...")
        
        from firecrawl import FirecrawlApp
        
        # 初始化客户端
        api_key = "fc-5cf3a134f43a4a5ca2fd753d1590e8fd"
        app = FirecrawlApp(api_key=api_key)
        
        # 测试目标URL
        target_url = "http://172.18.1.16/phone/20201227.html"
        print(f"🌐 测试爬取目标URL: {target_url}")
        
        result = app.scrape_url(target_url, formats=['html'])
        
        if result and 'html' in result:
            html_content = result['html']
            print(f"✅ 目标URL爬取成功! 内容长度: {len(html_content)} 字符")
            
            # 检查是否包含电话相关内容
            if "电话" in html_content or "矿领导" in html_content or "7719" in html_content:
                print("✅ 发现电话相关内容")
                
                # 简单统计电话号码
                import re
                phones = re.findall(r'\b77\d{5}\b', html_content)
                print(f"📞 发现 {len(phones)} 个可能的电话号码: {phones[:5]}...")
                
            else:
                print("⚠️ 未发现明显的电话相关内容")
            
            return html_content
        else:
            print("❌ 目标URL爬取失败")
            return None
            
    except Exception as e:
        print(f"❌ 目标URL测试失败: {e}")
        return None

def main():
    print("🧪 Firecrawl API 简单测试")
    print("=" * 50)
    
    # 测试1: 基本功能
    basic_ok = test_firecrawl()
    
    # 测试2: 目标URL
    if basic_ok:
        target_content = test_target_url()
        
        if target_content:
            print("\n🎉 所有测试通过! Firecrawl API工作正常")
            print("💡 现在可以运行完整的爬虫程序了")
        else:
            print("\n⚠️ 目标URL测试失败，但基本功能正常")
    else:
        print("\n❌ 基本功能测试失败，请检查API密钥和网络连接")

if __name__ == "__main__":
    main()
