#!/usr/bin/env python3
"""
Test script to verify crawl4ai connection and basic functionality
"""

import asyncio
import aiohttp
import logging
from config import CRAWL4AI_URL, API_TOKEN, BASE_URL

# Configure logging - only to file, no console output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('connection_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

async def test_crawl4ai_health():
    """Test crawl4ai service health"""
    try:
        async with aiohttp.ClientSession() as session:
            health_url = f"{CRAWL4AI_URL}/health"
            logger.info(f"Testing health endpoint: {health_url}")

            async with session.get(health_url, timeout=10) as response:
                if response.status == 200:
                    logger.info("SUCCESS: Crawl4AI service is healthy")
                    return True
                else:
                    logger.error(f"ERROR: Health check failed: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"ERROR: Failed to connect to Crawl4AI: {e}")
        return False

async def test_basic_crawl():
    """Test basic crawling functionality"""
    try:
        from crawl4ai import AsyncWebCrawler

        async with AsyncWebCrawler(
            api_endpoint=CRAWL4AI_URL,
            api_token=API_TOKEN,
            verbose=True
        ) as crawler:
            # Test with a simple page first
            test_url = "https://httpbin.org/html"
            logger.info(f"Testing basic crawl with: {test_url}")

            result = await crawler.arun(
                url=test_url,
                word_count_threshold=10,
                extraction_strategy="NoExtractionStrategy",
                bypass_cache=True
            )

            if result.success:
                logger.info("SUCCESS: Basic crawling test successful")
                logger.info(f"Content length: {len(result.cleaned_html)}")
                return True
            else:
                logger.error(f"ERROR: Basic crawling test failed: {result.error_message}")
                return False

    except Exception as e:
        logger.error(f"ERROR: Exception during basic crawl test: {e}")
        return False

async def test_target_url():
    """Test crawling the target URL"""
    try:
        from crawl4ai import AsyncWebCrawler

        async with AsyncWebCrawler(
            api_endpoint=CRAWL4AI_URL,
            api_token=API_TOKEN,
            verbose=True
        ) as crawler:
            logger.info(f"Testing target URL: {BASE_URL}")

            result = await crawler.arun(
                url=BASE_URL,
                word_count_threshold=10,
                extraction_strategy="NoExtractionStrategy",
                bypass_cache=True
            )

            if result.success:
                logger.info("SUCCESS: Target URL crawling successful")
                logger.info(f"Content length: {len(result.cleaned_html)}")

                # Check for expected content
                if "电话" in result.cleaned_html or "phone" in result.cleaned_html.lower():
                    logger.info("SUCCESS: Found phone-related content")
                else:
                    logger.warning("WARNING: No phone-related content found")

                return True
            else:
                logger.error(f"ERROR: Target URL crawling failed: {result.error_message}")
                return False

    except Exception as e:
        logger.error(f"ERROR: Exception during target URL test: {e}")
        return False

async def run_all_tests():
    """Run all connection tests"""
    logger.info("STARTING: Connection tests...")

    tests = [
        ("Health Check", test_crawl4ai_health),
        ("Basic Crawl", test_basic_crawl),
        ("Target URL", test_target_url)
    ]

    results = {}

    for test_name, test_func in tests:
        logger.info(f"RUNNING: {test_name} test...")
        try:
            result = await test_func()
            results[test_name] = result
            if result:
                logger.info(f"PASS: {test_name} test passed")
            else:
                logger.error(f"FAIL: {test_name} test failed")
        except Exception as e:
            logger.error(f"ERROR: {test_name} test error: {e}")
            results[test_name] = False

    # Summary
    logger.info("SUMMARY: Test Results Summary:")
    passed = sum(results.values())
    total = len(results)

    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        logger.info(f"  {test_name}: {status}")

    logger.info(f"Overall: {passed}/{total} tests passed")

    if passed == total:
        logger.info("SUCCESS: All tests passed! Ready to run the scraper.")
    else:
        logger.error("ERROR: Some tests failed. Please check the configuration.")

    return passed == total

if __name__ == "__main__":
    asyncio.run(run_all_tests())
