#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行政工作部电话信息爬虫
使用本地部署的crawl4ai爬取行政工作部电话信息
crawl4ai地址：172.18.151.239:11235
目标：从final_units_data.csv读取行政工作部的子链接，爬取电话信息
输出格式：电话号码、单位名称、电话用户名
"""

import asyncio
import re
import json
import csv
import logging
from typing import List, Dict, Optional
import aiohttp
from bs4 import BeautifulSoup
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xingzheng_phone_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class XingzhengPhoneCrawler:
    def __init__(self, 
                 csv_file: str = "final_units_data.csv",
                 crawl4ai_url: str = "http://172.18.151.239:11235"):
        self.csv_file = csv_file
        self.crawl4ai_url = crawl4ai_url
        self.api_token = "123456"  # 根据实际情况调整
        self.phone_data = []
        
        # 电话号码匹配模式（7位或11位数字）
        self.phone_pattern = r'\b\d{7}\b|\b\d{11}\b'
        
        # 中文姓名匹配模式
        self.name_pattern = r'[\u4e00-\u9fff]{2,4}'

    def read_unit_link(self) -> Optional[str]:
        """从CSV文件读取行政工作部的子链接"""
        try:
            df = pd.read_csv(self.csv_file, encoding='utf-8')
            
            # 查找行政工作部的行
            xingzheng_row = df[df['单位名称'] == '行政工作部']
            
            if not xingzheng_row.empty:
                link = xingzheng_row.iloc[0]['子链接']
                logger.info(f"找到行政工作部链接: {link}")
                return link
            else:
                logger.error("未找到行政工作部的记录")
                return None
                
        except Exception as e:
            logger.error(f"读取CSV文件失败: {e}")
            return None

    async def test_crawl4ai_connection(self) -> bool:
        """测试crawl4ai服务连接"""
        try:
            async with aiohttp.ClientSession() as session:
                health_url = f"{self.crawl4ai_url}/health"
                async with session.get(health_url, timeout=10) as response:
                    if response.status == 200:
                        logger.info("crawl4ai服务连接成功")
                        return True
                    else:
                        logger.error(f"crawl4ai健康检查失败: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"无法连接到crawl4ai服务: {e}")
            return False

    async def crawl_with_crawl4ai(self, url: str) -> Optional[str]:
        """使用crawl4ai API爬取网页"""
        try:
            async with aiohttp.ClientSession() as session:
                api_url = f"{self.crawl4ai_url}/crawl"
                headers = {
                    'Authorization': f'Bearer {self.api_token}',
                    'Content-Type': 'application/json'
                }

                # crawl4ai API请求参数
                payload = {
                    'urls': [url],
                    'bypass_cache': True,
                    'include_raw_html': True,
                    'browser_config': {
                        'headless': True,
                        'viewport': {'width': 1200, 'height': 800}
                    },
                    'crawler_config': {
                        'word_count_threshold': 1,
                        'remove_overlay_elements': True
                    }
                }

                logger.info(f"使用crawl4ai爬取: {url}")

                async with session.post(api_url, json=payload, headers=headers, timeout=60) as response:
                    if response.status == 200:
                        result = await response.json()

                        # 检查是否是异步任务
                        if 'task_id' in result:
                            task_id = result['task_id']
                            logger.info(f"获得异步任务ID: {task_id}")

                            # 轮询任务状态
                            for attempt in range(30):  # 最多等待30次，每次2秒
                                await asyncio.sleep(2)

                                status_url = f"{self.crawl4ai_url}/task/{task_id}"
                                async with session.get(status_url, headers=headers, timeout=10) as status_response:
                                    if status_response.status == 200:
                                        status_result = await status_response.json()

                                        if status_result.get('status') == 'completed':
                                            logger.info("异步任务完成")

                                            # 获取HTML内容
                                            html_content = None
                                            if status_result.get('result') and status_result['result'].get('results'):
                                                first_result = status_result['result']['results'][0]
                                                html_content = (first_result.get('cleaned_html') or
                                                              first_result.get('raw_html') or
                                                              first_result.get('html', ''))
                                            elif status_result.get('results'):
                                                first_result = status_result['results'][0]
                                                html_content = (first_result.get('cleaned_html') or
                                                              first_result.get('raw_html') or
                                                              first_result.get('html', ''))

                                            if html_content:
                                                logger.info(f"成功获取网页内容，长度: {len(html_content)} 字符")
                                                return html_content
                                            else:
                                                logger.error("任务完成但无法找到HTML内容")
                                                return None

                                        elif status_result.get('status') == 'failed':
                                            logger.error(f"异步任务失败: {status_result.get('error', 'Unknown error')}")
                                            return None

                                        elif status_result.get('status') in ['pending', 'running']:
                                            logger.info(f"任务状态: {status_result.get('status')}, 继续等待...")
                                            continue

                            logger.error("异步任务超时")
                            return None

                        # 检查同步返回结果
                        elif result.get('success') and result.get('results'):
                            first_result = result['results'][0]
                            html_content = (first_result.get('cleaned_html') or
                                          first_result.get('raw_html') or
                                          first_result.get('html', ''))

                            if html_content:
                                logger.info(f"成功获取网页内容，长度: {len(html_content)} 字符")
                                return html_content
                            else:
                                logger.error("crawl4ai返回空内容")
                                return None
                        else:
                            error_msg = result.get('error', 'Unknown error')
                            logger.error(f"crawl4ai爬取失败: {error_msg}")
                            return None
                    else:
                        error_text = await response.text()
                        logger.error(f"crawl4ai API请求失败: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.error(f"crawl4ai爬取异常: {e}")
            return None

    def extract_phone_info(self, html: str) -> List[Dict]:
        """从HTML中提取电话信息"""
        soup = BeautifulSoup(html, 'html.parser')
        extracted_data = []

        logger.info("开始解析HTML内容，查找电话信息")

        # 获取所有文本内容
        text_content = soup.get_text()
        
        # 按行分割文本
        lines = text_content.split('\n')
        
        for line_idx, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # 查找电话号码
            phones = re.findall(self.phone_pattern, line)
            
            if phones:
                logger.info(f"第{line_idx+1}行找到电话: {phones}")
                logger.info(f"行内容: {line}")
                
                for phone in phones:
                    # 查找电话号码前的中文姓名
                    # 在电话号码前查找中文姓名
                    phone_pos = line.find(phone)
                    if phone_pos > 0:
                        # 获取电话号码前的文本
                        before_phone = line[:phone_pos]
                        
                        # 查找中文姓名（排除符号如":"）
                        names = re.findall(self.name_pattern, before_phone)
                        
                        # 取最后一个匹配的姓名（最接近电话号码的）
                        name = names[-1] if names else ""
                        
                        # 清理姓名，移除可能的职务词汇
                        name = self.clean_name(name)
                        
                        if name and self.is_valid_phone(phone):
                            phone_record = {
                                'phone': phone,
                                'unit': '行政工作部',  # 固定为行政工作部
                                'name': name,
                                'source_line': line  # 保存原始行数据用于调试
                            }
                            extracted_data.append(phone_record)
                            logger.info(f"提取成功: {phone} - {name} - 行政工作部")
                        else:
                            logger.warning(f"跳过无效数据 - 电话:{phone}, 姓名:{name}")

        # 如果没有找到数据，尝试表格解析
        if not extracted_data:
            logger.info("尝试表格解析")
            extracted_data = self.extract_from_tables(soup)

        logger.info(f"总共提取到 {len(extracted_data)} 条电话记录")
        return extracted_data

    def extract_from_tables(self, soup: BeautifulSoup) -> List[Dict]:
        """从表格中提取电话信息"""
        extracted_data = []
        tables = soup.find_all('table')
        
        logger.info(f"找到 {len(tables)} 个表格")
        
        for table_idx, table in enumerate(tables):
            rows = table.find_all('tr')
            logger.info(f"表格{table_idx+1}共有 {len(rows)} 行")
            
            for row_idx, row in enumerate(rows):
                cells = row.find_all(['td', 'th'])
                if len(cells) < 2:
                    continue
                    
                # 提取每行的所有文本
                cell_texts = [cell.get_text(strip=True) for cell in cells]
                row_text = ' '.join(cell_texts)
                
                # 查找电话号码
                phones = re.findall(self.phone_pattern, row_text)
                
                if phones:
                    logger.info(f"表格第{row_idx+1}行找到电话: {phones}")
                    
                    for phone in phones:
                        # 查找电话号码所在的单元格位置
                        phone_cell_idx = -1
                        for idx, cell_text in enumerate(cell_texts):
                            if phone in cell_text:
                                phone_cell_idx = idx
                                break
                        
                        # 提取姓名（通常在电话号码前面的单元格）
                        name = ""
                        if phone_cell_idx > 0:
                            name = cell_texts[phone_cell_idx - 1]
                        elif phone_cell_idx == 0 and len(cell_texts) > 1:
                            name = cell_texts[1]
                        
                        # 清理姓名
                        name = self.clean_name(name)
                        
                        if self.is_valid_phone(phone) and name:
                            phone_record = {
                                'phone': phone,
                                'unit': '行政工作部',
                                'name': name,
                                'source_line': row_text
                            }
                            extracted_data.append(phone_record)
                            logger.info(f"表格提取成功: {phone} - {name} - 行政工作部")
        
        return extracted_data

    def clean_name(self, name: str) -> str:
        """清理姓名数据"""
        if not name:
            return ""
        
        # 移除常见的非姓名文本和符号
        name = re.sub(r'(姓名|联系人|负责人|主任|经理|厂长|部长|科长|主管|副主任|副部长)', '', name)
        name = re.sub(r'[：:、，,\(\)（）\[\]【】\-\s]', '', name)
        name = re.sub(r'\d+', '', name)  # 移除数字
        name = name.strip()
        
        # 验证是否为有效姓名（2-4个中文字符）
        if 2 <= len(name) <= 4 and re.match(r'^[\u4e00-\u9fff]+$', name):
            return name
        
        return ""

    def is_valid_phone(self, phone: str) -> bool:
        """验证电话号码有效性"""
        # 7位内线电话或11位手机号码
        return len(phone) == 7 or len(phone) == 11

    async def scrape_xingzheng_phones(self) -> List[Dict]:
        """主要爬取函数"""
        logger.info("开始爬取行政工作部电话信息")
        
        # 读取行政工作部链接
        target_url = self.read_unit_link()
        if not target_url:
            logger.error("无法获取行政工作部链接")
            return []
        
        # 测试crawl4ai连接
        if not await self.test_crawl4ai_connection():
            logger.error("无法连接到crawl4ai服务，请检查服务是否正常运行")
            return []
        
        # 使用crawl4ai获取网页内容
        html_content = await self.crawl_with_crawl4ai(target_url)
        if not html_content:
            logger.error("crawl4ai无法获取网页内容")
            return []
        
        # 提取电话信息
        self.phone_data = self.extract_phone_info(html_content)
        
        logger.info(f"爬取完成，共提取 {len(self.phone_data)} 条电话记录")
        return self.phone_data

    def save_to_csv(self, filename: str = "xingzheng_phone_data.csv"):
        """保存数据到CSV文件"""
        if not self.phone_data:
            logger.warning("没有数据可保存")
            return
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['电话号码', '单位名称', '电话用户名']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for record in self.phone_data:
                writer.writerow({
                    '电话号码': record['phone'],
                    '单位名称': record['unit'],
                    '电话用户名': record['name']
                })
        
        logger.info(f"数据已保存到 {filename}")

    def save_to_json(self, filename: str = "xingzheng_phone_data.json"):
        """保存数据到JSON文件"""
        if not self.phone_data:
            logger.warning("没有数据可保存")
            return
        
        # 转换为所需格式
        formatted_data = []
        for record in self.phone_data:
            formatted_data.append({
                '电话号码': record['phone'],
                '单位名称': record['unit'],
                '电话用户名': record['name']
            })
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(formatted_data, jsonfile, ensure_ascii=False, indent=2)
        
        logger.info(f"数据已保存到 {filename}")

    def print_results(self):
        """打印结果"""
        if not self.phone_data:
            print("没有提取到数据")
            return
        
        print(f"\n行政工作部电话信息 (共 {len(self.phone_data)} 条记录):")
        print("=" * 70)
        print(f"{'序号':<4} {'电话号码':<12} {'单位名称':<12} {'电话用户名':<10}")
        print("=" * 70)
        
        for i, record in enumerate(self.phone_data, 1):
            print(f"{i:<4} {record['phone']:<12} {record['unit']:<12} {record['name']:<10}")
        
        print("=" * 70)
        print(f"\n数据来源: crawl4ai (服务地址: {self.crawl4ai_url})")


async def main():
    """主函数"""
    print("行政工作部电话信息爬虫")
    print("=" * 50)
    print(f"crawl4ai服务地址: 172.18.151.239:11235")
    print(f"数据源文件: final_units_data.csv")
    print(f"目标单位: 行政工作部")
    print("=" * 50)
    
    crawler = XingzhengPhoneCrawler()
    
    try:
        # 执行爬取
        data = await crawler.scrape_xingzheng_phones()
        
        if data:
            # 显示结果
            crawler.print_results()
            
            # 保存数据
            crawler.save_to_csv()
            crawler.save_to_json()
            
            print(f"\n数据文件已保存到当前目录:")
            print(f"  - xingzheng_phone_data.csv")
            print(f"  - xingzheng_phone_data.json")
            print(f"  - xingzheng_phone_crawler.log")
        else:
            print("没有提取到任何数据，请检查:")
            print("  1. crawl4ai服务是否正常运行")
            print("  2. 目标网页是否可访问")
            print("  3. final_units_data.csv文件是否存在且包含行政工作部数据")
            print("  4. 查看日志文件获取详细错误信息")
    
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行失败: {e}")
        logger.error(f"程序执行失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())